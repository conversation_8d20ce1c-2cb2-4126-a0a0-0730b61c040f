# Generated by Django 5.0.14 on 2025-07-27 07:59

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("exam", "0006_remove_auto_email_on_end"),
    ]

    operations = [
        migrations.AddField(
            model_name="level",
            name="choices",
            field=models.JSONField(
                blank=True,
                help_text="JSON object with choices for multiple choice questions. Format: {'A': 'Option 1', 'B': 'Option 2', ...}",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="level",
            name="explanation",
            field=models.TextField(
                blank=True,
                help_text="Optional explanation shown after answering (for learning purposes)",
            ),
        ),
        migrations.AddField(
            model_name="level",
            name="question_type",
            field=models.CharField(
                choices=[
                    ("text", "Text Answer"),
                    ("multiple_choice", "Multiple Choice"),
                    ("true_false", "True/False"),
                ],
                default="text",
                help_text="Type of question",
                max_length=20,
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="level",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="testversion",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="level",
            name="correct_answer",
            field=models.CharField(
                help_text="Correct answer for text questions or correct choice letter for multiple choice",
                max_length=500,
            ),
        ),
        migrations.AlterField(
            model_name="level",
            name="description",
            field=models.TextField(help_text="The actual question text"),
        ),
        migrations.AlterField(
            model_name="level",
            name="is_bonus",
            field=models.BooleanField(
                default=False,
                help_text="Bonus questions don't count toward base 100 points",
            ),
        ),
        migrations.AlterField(
            model_name="level",
            name="name",
            field=models.CharField(
                help_text="Question title/identifier", max_length=200
            ),
        ),
        migrations.AlterField(
            model_name="level",
            name="order",
            field=models.IntegerField(help_text="Order in the version"),
        ),
        migrations.AlterField(
            model_name="level",
            name="score",
            field=models.IntegerField(default=5, help_text="Points for this question"),
        ),
    ]
