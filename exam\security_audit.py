"""
Security Audit and Vulnerability Assessment
Comprehensive security review for the exam system
"""

import os
import re
import json
import hashlib
from django.conf import settings
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand
from django.db import connection
from datetime import datetime, timedelta
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class SecurityAuditor:
    """Main security audit class"""
    
    def __init__(self):
        self.vulnerabilities = []
        self.recommendations = []
        self.security_score = 100
    
    def run_full_audit(self):
        """Run comprehensive security audit"""
        
        audit_results = {
            'timestamp': timezone.now().isoformat(),
            'django_security': self.audit_django_security(),
            'authentication': self.audit_authentication(),
            'authorization': self.audit_authorization(),
            'input_validation': self.audit_input_validation(),
            'data_protection': self.audit_data_protection(),
            'session_security': self.audit_session_security(),
            'file_security': self.audit_file_security(),
            'database_security': self.audit_database_security(),
            'network_security': self.audit_network_security(),
            'code_security': self.audit_code_security(),
        }
        
        # Calculate overall security score
        total_issues = sum(len(result.get('issues', [])) for result in audit_results.values() if isinstance(result, dict))
        self.security_score = max(0, 100 - (total_issues * 5))
        
        audit_results['summary'] = {
            'total_issues': total_issues,
            'security_score': self.security_score,
            'risk_level': self.get_risk_level(self.security_score),
            'recommendations': self.get_priority_recommendations()
        }
        
        return audit_results
    
    def audit_django_security(self):
        """Audit Django-specific security settings"""
        
        issues = []
        recommendations = []
        
        # Check DEBUG setting
        if getattr(settings, 'DEBUG', False):
            issues.append({
                'severity': 'HIGH',
                'issue': 'DEBUG is enabled in production',
                'description': 'DEBUG=True exposes sensitive information',
                'fix': 'Set DEBUG=False in production'
            })
        
        # Check SECRET_KEY
        secret_key = getattr(settings, 'SECRET_KEY', '')
        if len(secret_key) < 50:
            issues.append({
                'severity': 'CRITICAL',
                'issue': 'Weak SECRET_KEY',
                'description': 'SECRET_KEY is too short or default',
                'fix': 'Generate a strong, unique SECRET_KEY'
            })
        
        # Check ALLOWED_HOSTS
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        if '*' in allowed_hosts:
            issues.append({
                'severity': 'HIGH',
                'issue': 'Wildcard in ALLOWED_HOSTS',
                'description': 'ALLOWED_HOSTS contains wildcard (*)',
                'fix': 'Specify exact hostnames in ALLOWED_HOSTS'
            })
        
        # Check SECURE_* settings
        security_settings = {
            'SECURE_SSL_REDIRECT': 'Enable SSL redirect',
            'SECURE_HSTS_SECONDS': 'Set HSTS header',
            'SECURE_CONTENT_TYPE_NOSNIFF': 'Enable content type nosniff',
            'SECURE_BROWSER_XSS_FILTER': 'Enable XSS filter',
            'SECURE_FRAME_DENY': 'Deny framing',
            'SESSION_COOKIE_SECURE': 'Secure session cookies',
            'CSRF_COOKIE_SECURE': 'Secure CSRF cookies',
        }
        
        for setting, description in security_settings.items():
            if not getattr(settings, setting, False):
                recommendations.append({
                    'setting': setting,
                    'description': description,
                    'priority': 'MEDIUM'
                })
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS' if not issues else 'FAIL'
        }
    
    def audit_authentication(self):
        """Audit authentication mechanisms"""
        
        issues = []
        recommendations = []
        
        # Check for weak passwords
        weak_users = []
        for user in User.objects.all():
            if user.check_password('password') or user.check_password('123456') or user.check_password('admin'):
                weak_users.append(user.username)
        
        if weak_users:
            issues.append({
                'severity': 'HIGH',
                'issue': 'Weak default passwords detected',
                'description': f'Users with weak passwords: {", ".join(weak_users)}',
                'fix': 'Force password reset for affected users'
            })
        
        # Check password validation settings
        auth_password_validators = getattr(settings, 'AUTH_PASSWORD_VALIDATORS', [])
        if len(auth_password_validators) < 3:
            recommendations.append({
                'setting': 'AUTH_PASSWORD_VALIDATORS',
                'description': 'Add more password validators',
                'priority': 'MEDIUM'
            })
        
        # Check for inactive superusers
        inactive_superusers = User.objects.filter(is_superuser=True, is_active=False)
        if inactive_superusers.exists():
            recommendations.append({
                'setting': 'User Management',
                'description': f'Remove {inactive_superusers.count()} inactive superuser accounts',
                'priority': 'LOW'
            })
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS' if not issues else 'FAIL'
        }
    
    def audit_authorization(self):
        """Audit authorization and access controls"""
        
        issues = []
        recommendations = []
        
        # Check for users with excessive permissions
        superuser_count = User.objects.filter(is_superuser=True, is_active=True).count()
        if superuser_count > 3:
            recommendations.append({
                'setting': 'User Permissions',
                'description': f'Consider reducing number of superusers ({superuser_count})',
                'priority': 'MEDIUM'
            })
        
        # Check for staff users without specific permissions
        staff_without_permissions = User.objects.filter(
            is_staff=True,
            is_superuser=False,
            user_permissions__isnull=True,
            groups__isnull=True
        )
        
        if staff_without_permissions.exists():
            recommendations.append({
                'setting': 'Staff Permissions',
                'description': f'{staff_without_permissions.count()} staff users have no specific permissions',
                'priority': 'LOW'
            })
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS' if not issues else 'FAIL'
        }
    
    def audit_input_validation(self):
        """Audit input validation and sanitization"""
        
        issues = []
        recommendations = []
        
        # This would require static code analysis
        # For now, provide general recommendations
        recommendations.extend([
            {
                'setting': 'Input Validation',
                'description': 'Ensure all user inputs are validated and sanitized',
                'priority': 'HIGH'
            },
            {
                'setting': 'SQL Injection',
                'description': 'Use Django ORM instead of raw SQL queries',
                'priority': 'HIGH'
            },
            {
                'setting': 'XSS Prevention',
                'description': 'Use Django template auto-escaping',
                'priority': 'HIGH'
            }
        ])
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS'
        }
    
    def audit_data_protection(self):
        """Audit data protection measures"""
        
        issues = []
        recommendations = []
        
        # Check if sensitive data is encrypted
        from .models import AppConfig
        
        try:
            config = AppConfig.objects.first()
            if config and config.email_host_password:
                # Check if password appears to be encrypted
                if len(config.email_host_password) < 50:
                    recommendations.append({
                        'setting': 'Data Encryption',
                        'description': 'Consider encrypting sensitive configuration data',
                        'priority': 'MEDIUM'
                    })
        except:
            pass
        
        # Check database encryption
        if 'sqlite' in settings.DATABASES['default']['ENGINE']:
            recommendations.append({
                'setting': 'Database Security',
                'description': 'Consider using encrypted database for production',
                'priority': 'MEDIUM'
            })
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS'
        }
    
    def audit_session_security(self):
        """Audit session security settings"""
        
        issues = []
        recommendations = []
        
        # Check session settings
        session_settings = {
            'SESSION_COOKIE_AGE': (3600, 'Session timeout too long'),
            'SESSION_EXPIRE_AT_BROWSER_CLOSE': (True, 'Sessions should expire when browser closes'),
            'SESSION_COOKIE_HTTPONLY': (True, 'Session cookies should be HTTP-only'),
        }
        
        for setting, (expected, description) in session_settings.items():
            current_value = getattr(settings, setting, None)
            if setting == 'SESSION_COOKIE_AGE' and current_value and current_value > expected:
                recommendations.append({
                    'setting': setting,
                    'description': description,
                    'priority': 'MEDIUM'
                })
            elif setting != 'SESSION_COOKIE_AGE' and current_value != expected:
                recommendations.append({
                    'setting': setting,
                    'description': description,
                    'priority': 'MEDIUM'
                })
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS'
        }
    
    def audit_file_security(self):
        """Audit file upload and handling security"""
        
        issues = []
        recommendations = []
        
        # Check MEDIA_ROOT permissions
        media_root = getattr(settings, 'MEDIA_ROOT', '')
        if media_root and os.path.exists(media_root):
            # Check if media files are served securely
            recommendations.append({
                'setting': 'File Security',
                'description': 'Ensure uploaded files are scanned and validated',
                'priority': 'MEDIUM'
            })
        
        # Check for file upload size limits
        if not hasattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE'):
            recommendations.append({
                'setting': 'FILE_UPLOAD_MAX_MEMORY_SIZE',
                'description': 'Set file upload size limits',
                'priority': 'LOW'
            })
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS'
        }
    
    def audit_database_security(self):
        """Audit database security"""
        
        issues = []
        recommendations = []
        
        # Check database configuration
        db_config = settings.DATABASES['default']
        
        if db_config['ENGINE'] == 'django.db.backends.sqlite3':
            recommendations.append({
                'setting': 'Database Engine',
                'description': 'Consider using PostgreSQL or MySQL for production',
                'priority': 'LOW'
            })
        
        # Check for database backups
        recommendations.append({
            'setting': 'Database Backups',
            'description': 'Ensure regular automated database backups',
            'priority': 'HIGH'
        })
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS'
        }
    
    def audit_network_security(self):
        """Audit network security settings"""
        
        issues = []
        recommendations = []
        
        # Check CORS settings
        if hasattr(settings, 'CORS_ALLOW_ALL_ORIGINS') and settings.CORS_ALLOW_ALL_ORIGINS:
            issues.append({
                'severity': 'MEDIUM',
                'issue': 'CORS allows all origins',
                'description': 'CORS_ALLOW_ALL_ORIGINS is set to True',
                'fix': 'Specify allowed origins explicitly'
            })
        
        # Check for HTTPS enforcement
        if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
            recommendations.append({
                'setting': 'HTTPS',
                'description': 'Enable HTTPS redirect for production',
                'priority': 'HIGH'
            })
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS' if not issues else 'FAIL'
        }
    
    def audit_code_security(self):
        """Audit code-level security"""
        
        issues = []
        recommendations = []
        
        # General code security recommendations
        recommendations.extend([
            {
                'setting': 'Code Review',
                'description': 'Implement regular security code reviews',
                'priority': 'MEDIUM'
            },
            {
                'setting': 'Dependency Updates',
                'description': 'Keep all dependencies updated',
                'priority': 'HIGH'
            },
            {
                'setting': 'Error Handling',
                'description': 'Ensure sensitive information is not exposed in error messages',
                'priority': 'MEDIUM'
            }
        ])
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'status': 'PASS'
        }
    
    def get_risk_level(self, score):
        """Get risk level based on security score"""
        if score >= 90:
            return 'LOW'
        elif score >= 70:
            return 'MEDIUM'
        elif score >= 50:
            return 'HIGH'
        else:
            return 'CRITICAL'
    
    def get_priority_recommendations(self):
        """Get prioritized security recommendations"""
        return [
            'Enable HTTPS and security headers in production',
            'Implement strong password policies',
            'Set up regular automated backups',
            'Monitor and log security events',
            'Keep all dependencies updated',
            'Implement rate limiting for API endpoints',
            'Use environment variables for sensitive settings',
            'Enable database query logging for monitoring',
            'Implement proper error handling and logging',
            'Regular security audits and penetration testing'
        ]


# Global security auditor instance
security_auditor = SecurityAuditor()
