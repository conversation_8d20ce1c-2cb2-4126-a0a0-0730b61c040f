{% extends 'admin/base_admin.html' %}

{% block title %}Communications - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">💬</span>
            Communications Center
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            Manage announcements, email templates, and chat rooms
        </p>
    </div>
    
    <div class="admin-header-actions">
        <button onclick="createAnnouncement()" class="btn btn-primary">
            📢 New Announcement
        </button>
    </div>
</div>

<div class="admin-content">
    <!-- Communication Stats -->
    <div class="admin-grid admin-grid-3">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📊 Statistics</h3>
            </div>
            
            <div class="admin-card-content">
                <div class="stat-item">
                    <span class="stat-label">Active Announcements:</span>
                    <span class="stat-value">{{ announcements.count }}</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Email Templates:</span>
                    <span class="stat-value">{{ email_templates.count }}</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Chat Rooms:</span>
                    <span class="stat-value">{{ chat_rooms.count }}</span>
                </div>
            </div>
        </div>
        
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📈 Activity</h3>
            </div>
            
            <div class="admin-card-content">
                <div class="stat-item">
                    <span class="stat-label">Messages Today:</span>
                    <span class="stat-value">{{ messages_today }}</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Active Users:</span>
                    <span class="stat-value">{{ active_users }}</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Email Notifications:</span>
                    <span class="stat-value">{{ email_notifications }}</span>
                </div>
            </div>
        </div>
        
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">🔧 Quick Actions</h3>
            </div>
            
            <div class="admin-card-content">
                <div class="quick-actions">
                    <button onclick="createEmailTemplate()" class="btn btn-secondary btn-sm">
                        📧 New Email Template
                    </button>
                    
                    <button onclick="createChatRoom()" class="btn btn-secondary btn-sm">
                        💬 New Chat Room
                    </button>
                    
                    <button onclick="viewNotificationSettings()" class="btn btn-secondary btn-sm">
                        🔔 Notification Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Announcements -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0;">📢 Recent Announcements</h3>
        </div>
        
        <div class="admin-card-content">
            {% if announcements %}
                <div class="announcements-list">
                    {% for announcement in announcements %}
                        <div class="announcement-item">
                            <div class="announcement-header">
                                <div class="announcement-title">{{ announcement.title }}</div>
                                <div class="announcement-priority priority-{{ announcement.priority|lower }}">
                                    {{ announcement.priority }}
                                </div>
                            </div>
                            
                            <div class="announcement-content">
                                {{ announcement.content|truncatewords:20 }}
                            </div>
                            
                            <div class="announcement-meta">
                                <span>Created: {{ announcement.created_at|date:"M d, Y H:i" }}</span>
                                <span>By: {{ announcement.created_by.username }}</span>
                                {% if announcement.is_active %}
                                    <span class="status-badge active">Active</span>
                                {% else %}
                                    <span class="status-badge inactive">Inactive</span>
                                {% endif %}
                            </div>
                            
                            <div class="announcement-actions">
                                <button onclick="editAnnouncement({{ announcement.id }})" class="btn btn-secondary btn-sm">
                                    ✏️ Edit
                                </button>
                                
                                <button onclick="deleteAnnouncement({{ announcement.id }})" class="btn btn-danger btn-sm">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">📢</div>
                    <div class="empty-text">No announcements yet</div>
                    <div class="empty-subtext">Create your first announcement to get started</div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Email Templates -->
    <div class="admin-grid admin-grid-2">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📧 Email Templates</h3>
            </div>
            
            <div class="admin-card-content">
                {% if email_templates %}
                    <div class="templates-list">
                        {% for template in email_templates %}
                            <div class="template-item">
                                <div class="template-name">{{ template.name }}</div>
                                <div class="template-subject">{{ template.subject }}</div>
                                <div class="template-actions">
                                    <button onclick="editTemplate({{ template.id }})" class="btn btn-secondary btn-sm">
                                        ✏️ Edit
                                    </button>
                                    <button onclick="previewTemplate({{ template.id }})" class="btn btn-info btn-sm">
                                        👁️ Preview
                                    </button>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-text">No email templates</div>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">💬 Chat Rooms</h3>
            </div>
            
            <div class="admin-card-content">
                {% if chat_rooms %}
                    <div class="chatrooms-list">
                        {% for room in chat_rooms %}
                            <div class="chatroom-item">
                                <div class="chatroom-name">{{ room.name }}</div>
                                <div class="chatroom-stats">
                                    <span>{{ room.participants.count }} participants</span>
                                    <span>{{ room.messages.count }} messages</span>
                                </div>
                                <div class="chatroom-actions">
                                    <button onclick="manageChatRoom({{ room.id }})" class="btn btn-secondary btn-sm">
                                        ⚙️ Manage
                                    </button>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-text">No chat rooms</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-muted);
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
}

.quick-actions {
    display: grid;
    gap: 0.5rem;
}

.announcements-list {
    display: grid;
    gap: 1rem;
}

.announcement-item {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.announcement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.announcement-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.announcement-priority {
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-high {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.priority-medium {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.priority-low {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.announcement-content {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.announcement-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-badge.inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.announcement-actions {
    display: flex;
    gap: 0.5rem;
}

.templates-list,
.chatrooms-list {
    display: grid;
    gap: 1rem;
}

.template-item,
.chatroom-item {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.template-name,
.chatroom-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.template-subject {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.chatroom-stats {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.template-actions,
.chatroom-actions {
    display: flex;
    gap: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-text {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-subtext {
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function createAnnouncement() {
    alert('Create announcement feature coming soon!');
}

function createEmailTemplate() {
    alert('Create email template feature coming soon!');
}

function createChatRoom() {
    alert('Create chat room feature coming soon!');
}

function viewNotificationSettings() {
    alert('Notification settings feature coming soon!');
}

function editAnnouncement(id) {
    alert('Edit announcement feature coming soon!');
}

function deleteAnnouncement(id) {
    if (confirm('Are you sure you want to delete this announcement?')) {
        alert('Delete announcement feature coming soon!');
    }
}

function editTemplate(id) {
    alert('Edit template feature coming soon!');
}

function previewTemplate(id) {
    alert('Preview template feature coming soon!');
}

function manageChatRoom(id) {
    alert('Manage chat room feature coming soon!');
}
</script>
{% endblock %}
