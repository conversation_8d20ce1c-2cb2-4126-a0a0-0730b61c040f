{% extends 'admin/base_admin.html' %}

{% block title %}Test Version Creation Wizard - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">🧙‍♂️</span>
            Test Version Creation Wizard
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            Create a new test version with guided steps and templates
        </p>
    </div>
</div>

<div class="admin-content">
    <!-- Wizard Progress -->
    <div class="wizard-progress">
        <div class="progress-step active" data-step="1">
            <div class="step-number">1</div>
            <div class="step-label">Basic Info</div>
        </div>
        <div class="progress-step" data-step="2">
            <div class="step-number">2</div>
            <div class="step-label">Template</div>
        </div>
        <div class="progress-step" data-step="3">
            <div class="step-number">3</div>
            <div class="step-label">Questions</div>
        </div>
        <div class="progress-step" data-step="4">
            <div class="step-number">4</div>
            <div class="step-label">Settings</div>
        </div>
        <div class="progress-step" data-step="5">
            <div class="step-number">5</div>
            <div class="step-label">Review</div>
        </div>
    </div>

    <!-- Wizard Form -->
    <form id="wizardForm" method="post" class="wizard-form">
        {% csrf_token %}
        
        <!-- Step 1: Basic Information -->
        <div class="wizard-step active" data-step="1">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 style="color: var(--text-primary); margin: 0;">📝 Basic Information</h3>
                    <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
                        Enter the basic details for your test version
                    </p>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name" class="form-label">Test Name *</label>
                        <input type="text" id="name" name="name" class="form-input" required 
                               placeholder="e.g., Python Programming Fundamentals">
                        <div class="form-help">Choose a descriptive name for your test</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" name="description" class="form-textarea" rows="3"
                                  placeholder="Brief description of what this test covers..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="subject" class="form-label">Subject/Category</label>
                        <select id="subject" name="subject" class="form-input">
                            <option value="">Select a subject...</option>
                            <option value="programming">Programming</option>
                            <option value="mathematics">Mathematics</option>
                            <option value="science">Science</option>
                            <option value="language">Language Arts</option>
                            <option value="history">History</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="difficulty" class="form-label">Difficulty Level</label>
                        <select id="difficulty" name="difficulty" class="form-input">
                            <option value="beginner">Beginner</option>
                            <option value="intermediate" selected>Intermediate</option>
                            <option value="advanced">Advanced</option>
                            <option value="expert">Expert</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Template Selection -->
        <div class="wizard-step" data-step="2">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 style="color: var(--text-primary); margin: 0;">🎨 Choose Template</h3>
                    <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
                        Select a template to get started quickly
                    </p>
                </div>
                
                <div class="template-grid">
                    <div class="template-card" data-template="blank">
                        <div class="template-icon">📄</div>
                        <div class="template-name">Blank Template</div>
                        <div class="template-description">Start from scratch with no pre-defined questions</div>
                        <div class="template-stats">
                            <span>0 questions</span>
                        </div>
                    </div>
                    
                    <div class="template-card" data-template="programming">
                        <div class="template-icon">💻</div>
                        <div class="template-name">Programming Quiz</div>
                        <div class="template-description">Template for programming assessments with code questions</div>
                        <div class="template-stats">
                            <span>10 questions</span> • <span>Mix of types</span>
                        </div>
                    </div>
                    
                    <div class="template-card" data-template="multiple_choice">
                        <div class="template-icon">☑️</div>
                        <div class="template-name">Multiple Choice</div>
                        <div class="template-description">Standard multiple choice question format</div>
                        <div class="template-stats">
                            <span>15 questions</span> • <span>Multiple choice</span>
                        </div>
                    </div>
                    
                    <div class="template-card" data-template="mixed">
                        <div class="template-icon">🎯</div>
                        <div class="template-name">Mixed Format</div>
                        <div class="template-description">Combination of different question types</div>
                        <div class="template-stats">
                            <span>12 questions</span> • <span>Mixed types</span>
                        </div>
                    </div>
                    
                    <div class="template-card" data-template="import">
                        <div class="template-icon">📥</div>
                        <div class="template-name">Import from File</div>
                        <div class="template-description">Import questions from CSV or existing test</div>
                        <div class="template-stats">
                            <span>Custom</span>
                        </div>
                    </div>
                </div>
                
                <input type="hidden" id="selectedTemplate" name="template" value="blank">
            </div>
        </div>

        <!-- Step 3: Questions Configuration -->
        <div class="wizard-step" data-step="3">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 style="color: var(--text-primary); margin: 0;">❓ Questions Setup</h3>
                    <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
                        Configure your questions and scoring
                    </p>
                </div>
                
                <div class="questions-config">
                    <div class="config-section">
                        <h4>Question Distribution</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="totalQuestions" class="form-label">Total Questions</label>
                                <input type="number" id="totalQuestions" name="total_questions" 
                                       class="form-input" value="10" min="1" max="100">
                            </div>
                            
                            <div class="form-group">
                                <label for="bonusQuestions" class="form-label">Bonus Questions</label>
                                <input type="number" id="bonusQuestions" name="bonus_questions" 
                                       class="form-input" value="0" min="0" max="20">
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>Question Types</h4>
                        <div class="question-types">
                            <div class="type-option">
                                <input type="checkbox" id="typeText" name="question_types" value="text" checked>
                                <label for="typeText">Text Questions</label>
                                <span class="type-count">5</span>
                            </div>
                            
                            <div class="type-option">
                                <input type="checkbox" id="typeMultiple" name="question_types" value="multiple_choice" checked>
                                <label for="typeMultiple">Multiple Choice</label>
                                <span class="type-count">3</span>
                            </div>
                            
                            <div class="type-option">
                                <input type="checkbox" id="typeCode" name="question_types" value="code">
                                <label for="typeCode">Code Execution</label>
                                <span class="type-count">1</span>
                            </div>
                            
                            <div class="type-option">
                                <input type="checkbox" id="typeDrag" name="question_types" value="drag_drop">
                                <label for="typeDrag">Drag & Drop</label>
                                <span class="type-count">1</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>Scoring Configuration</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="maxScore" class="form-label">Maximum Score</label>
                                <input type="number" id="maxScore" name="max_score" 
                                       class="form-input" value="100" min="1">
                            </div>
                            
                            <div class="form-group">
                                <label for="passingScore" class="form-label">Passing Score (%)</label>
                                <input type="number" id="passingScore" name="passing_score" 
                                       class="form-input" value="70" min="0" max="100">
                            </div>
                        </div>
                        
                        <div class="scoring-preview">
                            <h5>Score Distribution Preview</h5>
                            <div class="score-breakdown">
                                <div class="score-item">
                                    <span>Regular Questions:</span>
                                    <span id="regularScore">100 points</span>
                                </div>
                                <div class="score-item">
                                    <span>Bonus Questions:</span>
                                    <span id="bonusScore">0 points</span>
                                </div>
                                <div class="score-item total">
                                    <span>Total Possible:</span>
                                    <span id="totalScore">100 points</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Advanced Settings -->
        <div class="wizard-step" data-step="4">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 style="color: var(--text-primary); margin: 0;">⚙️ Advanced Settings</h3>
                    <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
                        Configure advanced options and restrictions
                    </p>
                </div>
                
                <div class="settings-grid">
                    <div class="settings-section">
                        <h4>Time Limits</h4>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableTimeLimit" name="enable_time_limit">
                                <span class="checkmark"></span>
                                Enable time limit
                            </label>
                        </div>
                        
                        <div class="form-group time-limit-config" style="display: none;">
                            <label for="timeLimit" class="form-label">Time Limit (minutes)</label>
                            <input type="number" id="timeLimit" name="time_limit" 
                                   class="form-input" value="60" min="1">
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h4>Question Behavior</h4>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="randomizeQuestions" name="randomize_questions">
                                <span class="checkmark"></span>
                                Randomize question order
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="allowReview" name="allow_review" checked>
                                <span class="checkmark"></span>
                                Allow question review
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="showResults" name="show_results" checked>
                                <span class="checkmark"></span>
                                Show results after completion
                            </label>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h4>Security</h4>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="preventCheating" name="prevent_cheating">
                                <span class="checkmark"></span>
                                Enable anti-cheating measures
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="lockdownMode" name="lockdown_mode">
                                <span class="checkmark"></span>
                                Lockdown mode (disable copy/paste)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 5: Review and Create -->
        <div class="wizard-step" data-step="5">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 style="color: var(--text-primary); margin: 0;">👀 Review & Create</h3>
                    <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
                        Review your test configuration before creating
                    </p>
                </div>
                
                <div class="review-sections">
                    <div class="review-section">
                        <h4>Basic Information</h4>
                        <div class="review-item">
                            <span class="review-label">Name:</span>
                            <span class="review-value" id="reviewName">-</span>
                        </div>
                        <div class="review-item">
                            <span class="review-label">Subject:</span>
                            <span class="review-value" id="reviewSubject">-</span>
                        </div>
                        <div class="review-item">
                            <span class="review-label">Difficulty:</span>
                            <span class="review-value" id="reviewDifficulty">-</span>
                        </div>
                    </div>
                    
                    <div class="review-section">
                        <h4>Questions & Scoring</h4>
                        <div class="review-item">
                            <span class="review-label">Total Questions:</span>
                            <span class="review-value" id="reviewTotalQuestions">-</span>
                        </div>
                        <div class="review-item">
                            <span class="review-label">Question Types:</span>
                            <span class="review-value" id="reviewQuestionTypes">-</span>
                        </div>
                        <div class="review-item">
                            <span class="review-label">Maximum Score:</span>
                            <span class="review-value" id="reviewMaxScore">-</span>
                        </div>
                    </div>
                    
                    <div class="review-section">
                        <h4>Settings</h4>
                        <div class="review-item">
                            <span class="review-label">Template:</span>
                            <span class="review-value" id="reviewTemplate">-</span>
                        </div>
                        <div class="review-item">
                            <span class="review-label">Time Limit:</span>
                            <span class="review-value" id="reviewTimeLimit">-</span>
                        </div>
                        <div class="review-item">
                            <span class="review-label">Special Features:</span>
                            <span class="review-value" id="reviewFeatures">-</span>
                        </div>
                    </div>
                </div>
                
                <div class="creation-options">
                    <h4>After Creation</h4>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="createInstance" name="create_instance" checked>
                            <span class="checkmark"></span>
                            Create exam instance immediately
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="openEditor" name="open_editor" checked>
                            <span class="checkmark"></span>
                            Open question editor after creation
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Wizard Navigation -->
        <div class="wizard-navigation">
            <button type="button" id="prevBtn" class="btn btn-secondary" style="display: none;">
                ← Previous
            </button>
            
            <div class="nav-spacer"></div>
            
            <button type="button" id="nextBtn" class="btn btn-primary">
                Next →
            </button>
            
            <button type="submit" id="createBtn" class="btn btn-success" style="display: none;">
                🚀 Create Test Version
            </button>
        </div>
    </form>
</div>

{% endblock %}

{% block extra_css %}
<style>
.wizard-progress {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    position: relative;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.progress-step.active {
    opacity: 1;
}

.progress-step.completed {
    opacity: 1;
    color: var(--primary);
}

.progress-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 1.5rem;
    right: -2rem;
    width: 4rem;
    height: 2px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 1;
}

.progress-step.completed:not(:last-child)::after {
    background: var(--primary);
}

.step-number {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: var(--primary);
    border-color: var(--primary);
    color: black;
}

.progress-step.completed .step-number {
    background: var(--primary);
    border-color: var(--primary);
    color: black;
}

.step-label {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
}

.wizard-form {
    position: relative;
}

.wizard-step {
    display: none;
    animation: fadeIn 0.3s ease;
}

.wizard-step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-help {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.template-card {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.template-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 215, 0, 0.5);
    transform: translateY(-2px);
}

.template-card.selected {
    background: rgba(255, 215, 0, 0.1);
    border-color: var(--primary);
}

.template-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.template-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.template-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.template-stats {
    font-size: 0.8rem;
    color: var(--primary);
    font-weight: 500;
}

.questions-config {
    display: grid;
    gap: 2rem;
}

.config-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.question-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.type-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.type-count {
    margin-left: auto;
    background: var(--primary);
    color: black;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.scoring-preview {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.score-breakdown {
    display: grid;
    gap: 0.5rem;
}

.score-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.25rem;
}

.score-item.total {
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.3);
    font-weight: 600;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.settings-section {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: var(--text-primary);
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: var(--primary);
}

.review-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.review-section {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.review-section h4 {
    color: var(--primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.review-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.review-item:last-child {
    border-bottom: none;
}

.review-label {
    color: var(--text-muted);
    font-weight: 500;
}

.review-value {
    color: var(--text-primary);
    font-weight: 600;
}

.creation-options {
    padding: 1.5rem;
    background: rgba(255, 215, 0, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.creation-options h4 {
    color: var(--primary);
    margin-bottom: 1rem;
}

.wizard-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-spacer {
    flex: 1;
}

.time-limit-config {
    margin-left: 2rem;
    margin-top: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentStep = 1;
const totalSteps = 5;

document.addEventListener('DOMContentLoaded', function() {
    initializeWizard();
    setupEventListeners();
    updateScorePreview();
});

function initializeWizard() {
    updateStepDisplay();
    updateNavigation();
}

function setupEventListeners() {
    // Navigation buttons
    document.getElementById('nextBtn').addEventListener('click', nextStep);
    document.getElementById('prevBtn').addEventListener('click', prevStep);
    
    // Template selection
    document.querySelectorAll('.template-card').forEach(card => {
        card.addEventListener('click', function() {
            selectTemplate(this.dataset.template);
        });
    });
    
    // Form inputs that affect scoring
    document.getElementById('totalQuestions').addEventListener('input', updateScorePreview);
    document.getElementById('bonusQuestions').addEventListener('input', updateScorePreview);
    document.getElementById('maxScore').addEventListener('input', updateScorePreview);
    
    // Question type checkboxes
    document.querySelectorAll('input[name="question_types"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateQuestionCounts);
    });
    
    // Time limit toggle
    document.getElementById('enableTimeLimit').addEventListener('change', function() {
        const config = document.querySelector('.time-limit-config');
        config.style.display = this.checked ? 'block' : 'none';
    });
    
    // Form validation
    document.getElementById('wizardForm').addEventListener('submit', handleSubmit);
}

function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStepDisplay();
            updateNavigation();
            
            if (currentStep === 5) {
                updateReview();
            }
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
        updateNavigation();
    }
}

function updateStepDisplay() {
    // Update progress indicators
    document.querySelectorAll('.progress-step').forEach((step, index) => {
        const stepNumber = index + 1;
        step.classList.remove('active', 'completed');
        
        if (stepNumber === currentStep) {
            step.classList.add('active');
        } else if (stepNumber < currentStep) {
            step.classList.add('completed');
        }
    });
    
    // Update wizard steps
    document.querySelectorAll('.wizard-step').forEach((step, index) => {
        const stepNumber = index + 1;
        step.classList.remove('active');
        
        if (stepNumber === currentStep) {
            step.classList.add('active');
        }
    });
}

function updateNavigation() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const createBtn = document.getElementById('createBtn');
    
    // Previous button
    prevBtn.style.display = currentStep > 1 ? 'block' : 'none';
    
    // Next/Create buttons
    if (currentStep === totalSteps) {
        nextBtn.style.display = 'none';
        createBtn.style.display = 'block';
    } else {
        nextBtn.style.display = 'block';
        createBtn.style.display = 'none';
    }
}

function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            const name = document.getElementById('name').value.trim();
            if (!name) {
                alert('Please enter a test name');
                return false;
            }
            break;
        
        case 2:
            const template = document.getElementById('selectedTemplate').value;
            if (!template) {
                alert('Please select a template');
                return false;
            }
            break;
        
        case 3:
            const totalQuestions = parseInt(document.getElementById('totalQuestions').value);
            if (!totalQuestions || totalQuestions < 1) {
                alert('Please enter a valid number of questions');
                return false;
            }
            break;
    }
    
    return true;
}

function selectTemplate(templateName) {
    // Update visual selection
    document.querySelectorAll('.template-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    document.querySelector(`[data-template="${templateName}"]`).classList.add('selected');
    
    // Update hidden input
    document.getElementById('selectedTemplate').value = templateName;
    
    // Update question configuration based on template
    updateTemplateDefaults(templateName);
}

function updateTemplateDefaults(templateName) {
    const defaults = {
        'blank': {
            totalQuestions: 5,
            bonusQuestions: 0,
            questionTypes: ['text']
        },
        'programming': {
            totalQuestions: 10,
            bonusQuestions: 2,
            questionTypes: ['text', 'multiple_choice', 'code']
        },
        'multiple_choice': {
            totalQuestions: 15,
            bonusQuestions: 0,
            questionTypes: ['multiple_choice']
        },
        'mixed': {
            totalQuestions: 12,
            bonusQuestions: 3,
            questionTypes: ['text', 'multiple_choice', 'drag_drop']
        }
    };
    
    const config = defaults[templateName];
    if (config) {
        document.getElementById('totalQuestions').value = config.totalQuestions;
        document.getElementById('bonusQuestions').value = config.bonusQuestions;
        
        // Update question type checkboxes
        document.querySelectorAll('input[name="question_types"]').forEach(checkbox => {
            checkbox.checked = config.questionTypes.includes(checkbox.value);
        });
        
        updateQuestionCounts();
        updateScorePreview();
    }
}

function updateQuestionCounts() {
    const totalQuestions = parseInt(document.getElementById('totalQuestions').value) || 0;
    const checkedTypes = document.querySelectorAll('input[name="question_types"]:checked');
    
    if (checkedTypes.length > 0) {
        const questionsPerType = Math.floor(totalQuestions / checkedTypes.length);
        const remainder = totalQuestions % checkedTypes.length;
        
        checkedTypes.forEach((checkbox, index) => {
            const count = questionsPerType + (index < remainder ? 1 : 0);
            const countSpan = checkbox.closest('.type-option').querySelector('.type-count');
            countSpan.textContent = count;
        });
    }
    
    // Update unchecked types
    document.querySelectorAll('input[name="question_types"]:not(:checked)').forEach(checkbox => {
        const countSpan = checkbox.closest('.type-option').querySelector('.type-count');
        countSpan.textContent = '0';
    });
}

function updateScorePreview() {
    const totalQuestions = parseInt(document.getElementById('totalQuestions').value) || 0;
    const bonusQuestions = parseInt(document.getElementById('bonusQuestions').value) || 0;
    const maxScore = parseInt(document.getElementById('maxScore').value) || 100;
    
    const regularScore = maxScore;
    const bonusScore = bonusQuestions > 0 ? Math.round(maxScore * 0.2) : 0;
    const totalPossible = regularScore + bonusScore;
    
    document.getElementById('regularScore').textContent = `${regularScore} points`;
    document.getElementById('bonusScore').textContent = `${bonusScore} points`;
    document.getElementById('totalScore').textContent = `${totalPossible} points`;
}

function updateReview() {
    // Basic information
    document.getElementById('reviewName').textContent = document.getElementById('name').value || '-';
    document.getElementById('reviewSubject').textContent = 
        document.getElementById('subject').selectedOptions[0]?.text || '-';
    document.getElementById('reviewDifficulty').textContent = 
        document.getElementById('difficulty').selectedOptions[0]?.text || '-';
    
    // Questions & scoring
    document.getElementById('reviewTotalQuestions').textContent = 
        document.getElementById('totalQuestions').value || '-';
    
    const selectedTypes = Array.from(document.querySelectorAll('input[name="question_types"]:checked'))
        .map(cb => cb.nextElementSibling.textContent).join(', ');
    document.getElementById('reviewQuestionTypes').textContent = selectedTypes || '-';
    
    document.getElementById('reviewMaxScore').textContent = 
        document.getElementById('maxScore').value + ' points' || '-';
    
    // Settings
    const templateName = document.querySelector('.template-card.selected')?.querySelector('.template-name')?.textContent || '-';
    document.getElementById('reviewTemplate').textContent = templateName;
    
    const timeLimit = document.getElementById('enableTimeLimit').checked ? 
        document.getElementById('timeLimit').value + ' minutes' : 'No limit';
    document.getElementById('reviewTimeLimit').textContent = timeLimit;
    
    const features = [];
    if (document.getElementById('randomizeQuestions').checked) features.push('Randomized');
    if (document.getElementById('allowReview').checked) features.push('Review allowed');
    if (document.getElementById('preventCheating').checked) features.push('Anti-cheating');
    
    document.getElementById('reviewFeatures').textContent = features.join(', ') || 'None';
}

function handleSubmit(e) {
    e.preventDefault();
    
    if (!validateCurrentStep()) {
        return;
    }
    
    // Show loading state
    const createBtn = document.getElementById('createBtn');
    const originalText = createBtn.innerHTML;
    createBtn.innerHTML = '⏳ Creating...';
    createBtn.disabled = true;
    
    // Submit the form
    setTimeout(() => {
        e.target.submit();
    }, 500);
}
</script>
{% endblock %}
