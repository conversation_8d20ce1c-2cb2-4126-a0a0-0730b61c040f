"""
System Health Monitoring
Provides performance metrics tracking, error monitoring, and alerting
"""

import psutil
import time
import json
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from collections import defaultdict, deque
import threading
import os

logger = logging.getLogger(__name__)


class SystemMonitor:
    """Main system monitoring class"""
    
    def __init__(self):
        self.metrics_history = defaultdict(lambda: deque(maxlen=100))
        self.alerts = []
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Thresholds for alerts
        self.thresholds = {
            'cpu_usage': 80.0,          # CPU usage percentage
            'memory_usage': 85.0,       # Memory usage percentage
            'disk_usage': 90.0,         # Disk usage percentage
            'response_time': 2000,      # Response time in milliseconds
            'error_rate': 5.0,          # Error rate percentage
            'active_connections': 100,   # Database connections
        }
        
        # Performance counters
        self.performance_counters = {
            'total_requests': 0,
            'error_count': 0,
            'response_times': deque(maxlen=1000),
            'last_reset': timezone.now()
        }
    
    def get_system_metrics(self):
        """Get current system metrics"""
        
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available
            memory_total = memory.total
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free
            disk_total = disk.total
            
            # Network metrics
            network = psutil.net_io_counters()
            
            # Process metrics
            process = psutil.Process()
            process_memory = process.memory_info()
            
            metrics = {
                'timestamp': timezone.now().isoformat(),
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': cpu_count,
                    'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
                },
                'memory': {
                    'usage_percent': memory_percent,
                    'available_bytes': memory_available,
                    'total_bytes': memory_total,
                    'used_bytes': memory_total - memory_available
                },
                'disk': {
                    'usage_percent': disk_percent,
                    'free_bytes': disk_free,
                    'total_bytes': disk_total,
                    'used_bytes': disk_total - disk_free
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'process': {
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'cpu_percent': process.cpu_percent()
                }
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {str(e)}")
            return None
    
    def get_database_metrics(self):
        """Get database performance metrics"""
        
        try:
            with connection.cursor() as cursor:
                # Get database size (SQLite specific)
                if 'sqlite' in settings.DATABASES['default']['ENGINE']:
                    db_path = settings.DATABASES['default']['NAME']
                    db_size = os.path.getsize(db_path) if os.path.exists(db_path) else 0
                    
                    metrics = {
                        'database_size_bytes': db_size,
                        'connection_count': len(connection.queries),
                        'query_count': len(connection.queries),
                        'engine': 'SQLite'
                    }
                else:
                    # For other databases, provide basic metrics
                    metrics = {
                        'connection_count': 1,  # Simplified
                        'query_count': len(connection.queries),
                        'engine': settings.DATABASES['default']['ENGINE']
                    }
                
                # Add query performance
                recent_queries = connection.queries[-10:] if connection.queries else []
                query_times = [float(q['time']) for q in recent_queries if 'time' in q]
                
                metrics.update({
                    'recent_query_count': len(recent_queries),
                    'average_query_time': sum(query_times) / len(query_times) if query_times else 0,
                    'slowest_query_time': max(query_times) if query_times else 0
                })
                
                return metrics
                
        except Exception as e:
            logger.error(f"Error getting database metrics: {str(e)}")
            return {
                'error': str(e),
                'connection_count': 0,
                'query_count': 0
            }
    
    def get_application_metrics(self):
        """Get application-specific metrics"""
        
        try:
            from .models import Player, ExamInstance, User
            
            # Active sessions
            active_players = Player.objects.filter(
                start_time__isnull=False,
                end_time__isnull=True
            ).count()
            
            # Recent activity (last hour)
            one_hour_ago = timezone.now() - timedelta(hours=1)
            recent_logins = User.objects.filter(
                last_login__gte=one_hour_ago
            ).count()
            
            # Active instances
            active_instances = ExamInstance.objects.filter(
                is_active=True
            ).count()
            
            # Performance counters
            error_rate = 0
            if self.performance_counters['total_requests'] > 0:
                error_rate = (self.performance_counters['error_count'] / 
                            self.performance_counters['total_requests']) * 100
            
            avg_response_time = 0
            if self.performance_counters['response_times']:
                avg_response_time = sum(self.performance_counters['response_times']) / len(self.performance_counters['response_times'])
            
            metrics = {
                'active_exam_sessions': active_players,
                'recent_user_logins': recent_logins,
                'active_exam_instances': active_instances,
                'total_requests': self.performance_counters['total_requests'],
                'error_count': self.performance_counters['error_count'],
                'error_rate_percent': error_rate,
                'average_response_time_ms': avg_response_time,
                'uptime_seconds': (timezone.now() - self.performance_counters['last_reset']).total_seconds()
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting application metrics: {str(e)}")
            return {
                'error': str(e),
                'active_exam_sessions': 0,
                'recent_user_logins': 0,
                'active_exam_instances': 0
            }
    
    def get_cache_metrics(self):
        """Get cache performance metrics"""
        
        try:
            from .cache_utils import cache_manager
            
            # Get cache statistics
            cache_stats = cache_manager.get_stats()
            
            # Test cache performance
            start_time = time.time()
            cache.set('health_check_key', 'test_value', 60)
            cache.get('health_check_key')
            cache_response_time = (time.time() - start_time) * 1000
            
            metrics = {
                'cache_stats': cache_stats,
                'cache_response_time_ms': cache_response_time,
                'cache_available': True
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting cache metrics: {str(e)}")
            return {
                'cache_available': False,
                'error': str(e)
            }
    
    def check_health_status(self):
        """Perform comprehensive health check"""
        
        health_status = {
            'overall_status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'checks': {}
        }
        
        # System metrics check
        system_metrics = self.get_system_metrics()
        if system_metrics:
            health_status['checks']['system'] = {
                'status': 'healthy',
                'cpu_usage': system_metrics['cpu']['usage_percent'],
                'memory_usage': system_metrics['memory']['usage_percent'],
                'disk_usage': system_metrics['disk']['usage_percent']
            }
            
            # Check thresholds
            if system_metrics['cpu']['usage_percent'] > self.thresholds['cpu_usage']:
                health_status['checks']['system']['status'] = 'warning'
                health_status['overall_status'] = 'warning'
            
            if system_metrics['memory']['usage_percent'] > self.thresholds['memory_usage']:
                health_status['checks']['system']['status'] = 'critical'
                health_status['overall_status'] = 'critical'
        else:
            health_status['checks']['system'] = {'status': 'error', 'message': 'Unable to get system metrics'}
            health_status['overall_status'] = 'critical'
        
        # Database check
        db_metrics = self.get_database_metrics()
        if 'error' not in db_metrics:
            health_status['checks']['database'] = {
                'status': 'healthy',
                'connection_count': db_metrics.get('connection_count', 0),
                'average_query_time': db_metrics.get('average_query_time', 0)
            }
        else:
            health_status['checks']['database'] = {'status': 'error', 'message': db_metrics['error']}
            health_status['overall_status'] = 'critical'
        
        # Application check
        app_metrics = self.get_application_metrics()
        if 'error' not in app_metrics:
            health_status['checks']['application'] = {
                'status': 'healthy',
                'active_sessions': app_metrics.get('active_exam_sessions', 0),
                'error_rate': app_metrics.get('error_rate_percent', 0)
            }
            
            if app_metrics.get('error_rate_percent', 0) > self.thresholds['error_rate']:
                health_status['checks']['application']['status'] = 'warning'
                if health_status['overall_status'] == 'healthy':
                    health_status['overall_status'] = 'warning'
        else:
            health_status['checks']['application'] = {'status': 'error', 'message': app_metrics['error']}
            health_status['overall_status'] = 'critical'
        
        # Cache check
        cache_metrics = self.get_cache_metrics()
        health_status['checks']['cache'] = {
            'status': 'healthy' if cache_metrics.get('cache_available', False) else 'warning',
            'response_time': cache_metrics.get('cache_response_time_ms', 0)
        }
        
        return health_status
    
    def record_request(self, response_time_ms, is_error=False):
        """Record request metrics"""
        
        self.performance_counters['total_requests'] += 1
        self.performance_counters['response_times'].append(response_time_ms)
        
        if is_error:
            self.performance_counters['error_count'] += 1
    
    def get_performance_summary(self):
        """Get performance summary"""
        
        system_metrics = self.get_system_metrics()
        db_metrics = self.get_database_metrics()
        app_metrics = self.get_application_metrics()
        cache_metrics = self.get_cache_metrics()
        
        return {
            'timestamp': timezone.now().isoformat(),
            'system': system_metrics,
            'database': db_metrics,
            'application': app_metrics,
            'cache': cache_metrics,
            'health_status': self.check_health_status()
        }
    
    def reset_counters(self):
        """Reset performance counters"""
        
        self.performance_counters = {
            'total_requests': 0,
            'error_count': 0,
            'response_times': deque(maxlen=1000),
            'last_reset': timezone.now()
        }
        
        logger.info("Performance counters reset")


# Global monitor instance
system_monitor = SystemMonitor()


class PerformanceMiddleware:
    """Middleware to track request performance"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        end_time = time.time()
        response_time_ms = (end_time - start_time) * 1000
        
        is_error = response.status_code >= 400
        system_monitor.record_request(response_time_ms, is_error)
        
        return response
