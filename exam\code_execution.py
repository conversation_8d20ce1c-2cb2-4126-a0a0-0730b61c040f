"""
Safe Code Execution Engine
Provides secure code execution for programming questions
"""

import subprocess
import tempfile
import os
import json
import time
from django.conf import settings
import sys
from io import StringIO
import contextlib


class CodeExecutor:
    """Safe code execution with timeout and resource limits"""
    
    SUPPORTED_LANGUAGES = {
        'python': {
            'extension': '.py',
            'command': [sys.executable],
            'timeout': 10
        },
        'javascript': {
            'extension': '.js',
            'command': ['node'],
            'timeout': 10
        }
    }
    
    def __init__(self, language='python'):
        self.language = language.lower()
        if self.language not in self.SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {language}")
        
        self.config = self.SUPPORTED_LANGUAGES[self.language]
    
    def execute_code(self, code, test_cases=None, timeout=None):
        """
        Execute code safely with test cases
        
        Args:
            code (str): The code to execute
            test_cases (list): List of test cases with input/expected output
            timeout (int): Execution timeout in seconds
            
        Returns:
            dict: Execution results with success, output, errors, and test results
        """
        if timeout is None:
            timeout = self.config['timeout']
        
        results = {
            'success': False,
            'output': '',
            'errors': '',
            'execution_time': 0,
            'test_results': [],
            'score': 0,
            'total_tests': 0
        }
        
        try:
            if self.language == 'python':
                return self._execute_python_code(code, test_cases, timeout)
            elif self.language == 'javascript':
                return self._execute_javascript_code(code, test_cases, timeout)
            else:
                results['errors'] = f"Language {self.language} not implemented yet"
                return results
                
        except Exception as e:
            results['errors'] = f"Execution error: {str(e)}"
            return results
    
    def _execute_python_code(self, code, test_cases, timeout):
        """Execute Python code safely"""
        results = {
            'success': False,
            'output': '',
            'errors': '',
            'execution_time': 0,
            'test_results': [],
            'score': 0,
            'total_tests': 0
        }
        
        start_time = time.time()
        
        try:
            # Create a restricted execution environment
            restricted_globals = {
                '__builtins__': {
                    'print': print,
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'list': list,
                    'dict': dict,
                    'tuple': tuple,
                    'set': set,
                    'range': range,
                    'enumerate': enumerate,
                    'zip': zip,
                    'map': map,
                    'filter': filter,
                    'sorted': sorted,
                    'sum': sum,
                    'max': max,
                    'min': min,
                    'abs': abs,
                    'round': round,
                    'bool': bool,
                    'type': type,
                    'isinstance': isinstance,
                }
            }
            
            # Capture output
            output_buffer = StringIO()
            
            with contextlib.redirect_stdout(output_buffer):
                with contextlib.redirect_stderr(output_buffer):
                    # Execute the code
                    exec(code, restricted_globals)
            
            results['output'] = output_buffer.getvalue()
            results['success'] = True
            
            # Run test cases if provided
            if test_cases:
                results['total_tests'] = len(test_cases)
                passed_tests = 0
                
                for i, test_case in enumerate(test_cases):
                    test_result = self._run_python_test_case(code, test_case, restricted_globals)
                    results['test_results'].append(test_result)
                    
                    if test_result['passed']:
                        passed_tests += 1
                
                results['score'] = (passed_tests / len(test_cases)) * 100 if test_cases else 100
            else:
                results['score'] = 100  # Full score if no test cases
                
        except Exception as e:
            results['errors'] = str(e)
            results['success'] = False
        
        results['execution_time'] = time.time() - start_time
        return results
    
    def _run_python_test_case(self, code, test_case, restricted_globals):
        """Run a single Python test case"""
        test_result = {
            'input': test_case.get('input', ''),
            'expected': test_case.get('expected', ''),
            'actual': '',
            'passed': False,
            'error': ''
        }
        
        try:
            # Create a copy of globals for this test
            test_globals = restricted_globals.copy()
            
            # If there's input, we need to mock input() function
            if test_case.get('input'):
                input_values = test_case['input'].split('\n') if isinstance(test_case['input'], str) else [str(test_case['input'])]
                input_iter = iter(input_values)
                test_globals['__builtins__']['input'] = lambda prompt='': next(input_iter, '')
            
            # Capture output for this test
            test_output = StringIO()
            
            with contextlib.redirect_stdout(test_output):
                exec(code, test_globals)
            
            actual_output = test_output.getvalue().strip()
            expected_output = str(test_case.get('expected', '')).strip()
            
            test_result['actual'] = actual_output
            test_result['passed'] = actual_output == expected_output
            
        except Exception as e:
            test_result['error'] = str(e)
            test_result['passed'] = False
        
        return test_result
    
    def _execute_javascript_code(self, code, test_cases, timeout):
        """Execute JavaScript code using Node.js (if available)"""
        results = {
            'success': False,
            'output': '',
            'errors': '',
            'execution_time': 0,
            'test_results': [],
            'score': 0,
            'total_tests': 0
        }
        
        # For now, return a placeholder
        results['errors'] = "JavaScript execution not implemented yet"
        return results
    
    def validate_code(self, code):
        """Basic code validation"""
        if not code or not code.strip():
            return False, "Code cannot be empty"
        
        # Check for dangerous imports/functions
        dangerous_patterns = [
            'import os',
            'import sys',
            'import subprocess',
            'import socket',
            'import urllib',
            'import requests',
            'open(',
            'file(',
            'exec(',
            'eval(',
            '__import__',
        ]
        
        code_lower = code.lower()
        for pattern in dangerous_patterns:
            if pattern in code_lower:
                return False, f"Dangerous operation detected: {pattern}"
        
        return True, "Code validation passed"


class TestCaseGenerator:
    """Generate test cases for programming questions"""
    
    @staticmethod
    def create_simple_test_case(input_value, expected_output):
        """Create a simple test case"""
        return {
            'input': str(input_value),
            'expected': str(expected_output),
            'description': f"Input: {input_value}, Expected: {expected_output}"
        }
    
    @staticmethod
    def create_function_test_case(function_name, args, expected_result):
        """Create a test case for a specific function"""
        return {
            'function': function_name,
            'args': args,
            'expected': expected_result,
            'description': f"Test {function_name}({', '.join(map(str, args))}) = {expected_result}"
        }


# Example usage and templates
SAMPLE_QUESTIONS = {
    'hello_world': {
        'title': 'Hello World',
        'description': 'Write a program that prints "Hello, World!"',
        'starter_code': '# Write your code here\nprint("Hello, World!")',
        'test_cases': [
            TestCaseGenerator.create_simple_test_case('', 'Hello, World!')
        ]
    },
    'add_numbers': {
        'title': 'Add Two Numbers',
        'description': 'Write a function that adds two numbers and returns the result',
        'starter_code': '''def add_numbers(a, b):
    # Write your code here
    return a + b

# Test your function
result = add_numbers(5, 3)
print(result)''',
        'test_cases': [
            TestCaseGenerator.create_simple_test_case('', '8'),
        ]
    },
    'fibonacci': {
        'title': 'Fibonacci Sequence',
        'description': 'Write a function that returns the nth Fibonacci number',
        'starter_code': '''def fibonacci(n):
    # Write your code here
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Test your function
print(fibonacci(10))''',
        'test_cases': [
            TestCaseGenerator.create_simple_test_case('', '55'),
        ]
    }
}
