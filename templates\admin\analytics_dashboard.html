{% extends 'admin/base_admin.html' %}

{% block title %}Analytics Dashboard - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">📊</span>
            Advanced Analytics Dashboard
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            Real-time metrics, performance analytics, and trend analysis
        </p>
    </div>
    
    <div class="admin-header-actions">
        <select id="instanceFilter" class="form-input" style="width: 200px;">
            <option value="">All Instances</option>
            {% for instance in available_instances %}
                <option value="{{ instance.id }}" {% if instance.id|stringformat:"s" == selected_instance_id %}selected{% endif %}>
                    {{ instance.name }}
                </option>
            {% endfor %}
        </select>
        
        <button onclick="refreshAllData()" class="btn btn-secondary">
            🔄 Refresh Data
        </button>
        
        <button onclick="toggleAutoRefresh()" id="autoRefreshBtn" class="btn btn-primary">
            ▶️ Auto Refresh
        </button>
    </div>
</div>

<div class="admin-content">
    <!-- Real-time Metrics -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                <span style="font-size: 1.5rem;">⚡</span>
                Real-time Metrics
            </h3>
            <div class="metrics-timestamp" id="metricsTimestamp">
                Last updated: {{ real_time_metrics.timestamp|date:"H:i:s" }}
            </div>
        </div>
        
        <div class="metrics-grid" id="realTimeMetrics">
            <div class="metric-card">
                <div class="metric-icon">👥</div>
                <div class="metric-value">{{ real_time_metrics.active_students }}</div>
                <div class="metric-label">Active Students</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">✅</div>
                <div class="metric-value">{{ real_time_metrics.completed_exams }}</div>
                <div class="metric-label">Completed Exams</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">📈</div>
                <div class="metric-value">{{ real_time_metrics.average_score|floatformat:1 }}%</div>
                <div class="metric-label">Average Score</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">🎯</div>
                <div class="metric-value">{{ real_time_metrics.accuracy_rate|floatformat:1 }}%</div>
                <div class="metric-label">Accuracy Rate</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">🕐</div>
                <div class="metric-value">{{ real_time_metrics.students_joined_last_hour }}</div>
                <div class="metric-label">Joined Last Hour</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">📝</div>
                <div class="metric-value">{{ real_time_metrics.total_questions_answered }}</div>
                <div class="metric-label">Questions Answered</div>
            </div>
        </div>
    </div>

    <!-- Performance Analytics -->
    <div class="admin-grid admin-grid-2">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-size: 1.5rem;">📊</span>
                    Score Distribution
                </h3>
            </div>
            
            <div class="chart-container">
                <canvas id="scoreDistributionChart" width="400" height="200"></canvas>
            </div>
            
            <div class="score-breakdown">
                {% for grade, count in performance_analytics.score_distribution.items %}
                    <div class="score-item">
                        <span class="grade-label grade-{{ grade|lower }}">{{ grade }}</span>
                        <span class="grade-count">{{ count }} students</span>
                    </div>
                {% endfor %}
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-size: 1.5rem;">🎯</span>
                    Question Difficulty Analysis
                </h3>
            </div>
            
            <div class="difficulty-list" id="questionDifficulty">
                {% for question in performance_analytics.question_difficulty %}
                    <div class="difficulty-item">
                        <div class="question-info">
                            <div class="question-name">{{ question.question_name }}</div>
                            <div class="question-type">{{ question.question_type|title }}</div>
                        </div>
                        <div class="difficulty-metrics">
                            <div class="success-rate">
                                <span class="metric-label">Success Rate:</span>
                                <span class="metric-value">{{ question.success_rate|floatformat:1 }}%</span>
                            </div>
                            <div class="avg-time">
                                <span class="metric-label">Avg Time:</span>
                                <span class="metric-value">{{ question.average_time }}s</span>
                            </div>
                        </div>
                        <div class="difficulty-bar">
                            <div class="difficulty-fill" style="width: {{ question.difficulty_score }}%"></div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Trend Analysis -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                <span style="font-size: 1.5rem;">📈</span>
                Trend Analysis
            </h3>
            <div class="trend-controls">
                <select id="trendPeriod" class="form-input">
                    <option value="7" {% if trend_days == 7 %}selected{% endif %}>Last 7 days</option>
                    <option value="14" {% if trend_days == 14 %}selected{% endif %}>Last 14 days</option>
                    <option value="30" {% if trend_days == 30 %}selected{% endif %}>Last 30 days</option>
                </select>
            </div>
        </div>
        
        <div class="trend-summary">
            <div class="trend-metric">
                <div class="trend-icon">👥</div>
                <div class="trend-value">{{ trend_analysis.trends.total_period_students }}</div>
                <div class="trend-label">Total Students</div>
                <div class="trend-change {% if trend_analysis.trends.student_growth > 0 %}positive{% else %}negative{% endif %}">
                    {{ trend_analysis.trends.student_growth|floatformat:1 }}%
                </div>
            </div>
            
            <div class="trend-metric">
                <div class="trend-icon">✅</div>
                <div class="trend-value">{{ trend_analysis.trends.total_period_completions }}</div>
                <div class="trend-label">Completions</div>
            </div>
            
            <div class="trend-metric">
                <div class="trend-icon">📊</div>
                <div class="trend-value">{{ trend_analysis.trends.period_average_score|floatformat:1 }}%</div>
                <div class="trend-label">Avg Score</div>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="trendChart" width="800" height="300"></canvas>
        </div>
    </div>

    <!-- System Health -->
    <div class="admin-grid admin-grid-3">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-size: 1.5rem;">💓</span>
                    System Health
                </h3>
            </div>
            
            <div class="health-status {{ system_health.status }}">
                <div class="health-icon">
                    {% if system_health.status == 'healthy' %}✅{% elif system_health.status == 'warning' %}⚠️{% else %}❌{% endif %}
                </div>
                <div class="health-text">{{ system_health.status|title }}</div>
            </div>
            
            <div class="health-metrics">
                <div class="health-metric">
                    <span class="metric-label">Active Sessions:</span>
                    <span class="metric-value">{{ system_health.active_sessions }}</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">Response Time:</span>
                    <span class="metric-value">{{ system_health.response_time_estimate }}ms</span>
                </div>
                <div class="health-metric">
                    <span class="metric-label">Memory Usage:</span>
                    <span class="metric-value">{{ system_health.memory_usage_estimate|floatformat:1 }}%</span>
                </div>
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-size: 1.5rem;">👤</span>
                    User Engagement
                </h3>
            </div>
            
            <div class="engagement-metrics">
                <div class="engagement-metric">
                    <div class="metric-value">{{ engagement_metrics.user_activity.weekly_activity_rate|floatformat:1 }}%</div>
                    <div class="metric-label">Weekly Active</div>
                </div>
                <div class="engagement-metric">
                    <div class="metric-value">{{ engagement_metrics.exam_engagement.completion_rate|floatformat:1 }}%</div>
                    <div class="metric-label">Completion Rate</div>
                </div>
                <div class="engagement-metric">
                    <div class="metric-value">{{ engagement_metrics.communication_engagement.messages_per_user|floatformat:1 }}</div>
                    <div class="metric-label">Messages/User</div>
                </div>
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-size: 1.5rem;">⚡</span>
                    Quick Actions
                </h3>
            </div>
            
            <div class="quick-actions">
                <button onclick="exportAnalyticsData()" class="btn btn-secondary btn-sm">
                    📊 Export Data
                </button>
                <button onclick="generateReport()" class="btn btn-secondary btn-sm">
                    📄 Generate Report
                </button>
                <button onclick="openMonitoring()" class="btn btn-secondary btn-sm">
                    🔍 System Monitor
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 0.75rem;
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.metric-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.metric-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.25rem;
}

.metric-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.metrics-timestamp {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.chart-container {
    margin: 1rem 0;
    position: relative;
    height: 200px;
}

.score-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.grade-label {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.grade-a { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
.grade-b { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
.grade-c { background: rgba(251, 191, 36, 0.2); color: #fbbf24; }
.grade-d { background: rgba(249, 115, 22, 0.2); color: #f97316; }
.grade-f { background: rgba(239, 68, 68, 0.2); color: #ef4444; }

.difficulty-list {
    max-height: 400px;
    overflow-y: auto;
}

.difficulty-item {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.question-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.question-name {
    font-weight: 600;
    color: var(--text-primary);
}

.question-type {
    font-size: 0.8rem;
    color: var(--text-muted);
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.difficulty-metrics {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.difficulty-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.difficulty-fill {
    height: 100%;
    background: linear-gradient(90deg, #22c55e, #fbbf24, #ef4444);
    transition: width 0.3s ease;
}

.trend-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.trend-metric {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.trend-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.trend-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

.trend-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.trend-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.trend-change.positive {
    color: #22c55e;
}

.trend-change.negative {
    color: #ef4444;
}

.health-status {
    text-align: center;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.health-status.healthy {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.health-status.warning {
    background: rgba(251, 191, 36, 0.1);
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.health-status.critical {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.health-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.health-text {
    font-weight: 600;
    text-transform: uppercase;
}

.health-metrics {
    display: grid;
    gap: 0.5rem;
}

.health-metric {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.25rem;
}

.engagement-metrics {
    display: grid;
    gap: 1rem;
}

.engagement-metric {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.quick-actions {
    display: grid;
    gap: 0.5rem;
}

.admin-header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.trend-controls {
    display: flex;
    gap: 0.5rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let autoRefreshInterval = null;
let isAutoRefreshing = false;

// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    setupEventListeners();
});

function setupEventListeners() {
    document.getElementById('instanceFilter').addEventListener('change', function() {
        const instanceId = this.value;
        const url = new URL(window.location);
        if (instanceId) {
            url.searchParams.set('instance_id', instanceId);
        } else {
            url.searchParams.delete('instance_id');
        }
        window.location.href = url.toString();
    });
    
    document.getElementById('trendPeriod').addEventListener('change', function() {
        const days = this.value;
        const url = new URL(window.location);
        url.searchParams.set('trend_days', days);
        window.location.href = url.toString();
    });
}

function initializeCharts() {
    // Score Distribution Chart
    const scoreCtx = document.getElementById('scoreDistributionChart').getContext('2d');
    new Chart(scoreCtx, {
        type: 'doughnut',
        data: {
            labels: ['A (80-100%)', 'B (60-80%)', 'C (40-60%)', 'D (20-40%)', 'F (0-20%)'],
            datasets: [{
                data: [
                    {{ performance_analytics.score_distribution.A|default:0 }},
                    {{ performance_analytics.score_distribution.B|default:0 }},
                    {{ performance_analytics.score_distribution.C|default:0 }},
                    {{ performance_analytics.score_distribution.D|default:0 }},
                    {{ performance_analytics.score_distribution.F|default:0 }}
                ],
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(251, 191, 36, 0.8)',
                    'rgba(249, 115, 22, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgba(34, 197, 94, 1)',
                    'rgba(59, 130, 246, 1)',
                    'rgba(251, 191, 36, 1)',
                    'rgba(249, 115, 22, 1)',
                    'rgba(239, 68, 68, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: '#ffffff',
                        usePointStyle: true
                    }
                }
            }
        }
    });
    
    // Trend Chart
    const trendCtx = document.getElementById('trendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: [
                {% for day in trend_analysis.daily_stats %}
                    '{{ day.date }}'{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: 'New Students',
                data: [
                    {% for day in trend_analysis.daily_stats %}
                        {{ day.new_students }}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: 'rgba(255, 215, 0, 1)',
                backgroundColor: 'rgba(255, 215, 0, 0.1)',
                tension: 0.4
            }, {
                label: 'Completed Exams',
                data: [
                    {% for day in trend_analysis.daily_stats %}
                        {{ day.completed_exams }}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: 'rgba(34, 197, 94, 1)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            }
        }
    });
}

async function refreshAllData() {
    const instanceId = document.getElementById('instanceFilter').value;
    
    try {
        // Refresh real-time metrics
        const response = await fetch(`{% url "admin_panel:admin_analytics_api" %}?type=real_time&instance_id=${instanceId}`);
        const result = await response.json();
        
        if (result.success) {
            updateRealTimeMetrics(result.data);
            document.getElementById('metricsTimestamp').textContent = 
                `Last updated: ${new Date().toLocaleTimeString()}`;
        }
    } catch (error) {
        console.error('Error refreshing data:', error);
    }
}

function updateRealTimeMetrics(data) {
    const metrics = document.querySelectorAll('#realTimeMetrics .metric-value');
    if (metrics.length >= 6) {
        metrics[0].textContent = data.active_students;
        metrics[1].textContent = data.completed_exams;
        metrics[2].textContent = data.average_score.toFixed(1) + '%';
        metrics[3].textContent = data.accuracy_rate.toFixed(1) + '%';
        metrics[4].textContent = data.students_joined_last_hour;
        metrics[5].textContent = data.total_questions_answered;
    }
}

function toggleAutoRefresh() {
    const btn = document.getElementById('autoRefreshBtn');
    
    if (isAutoRefreshing) {
        clearInterval(autoRefreshInterval);
        isAutoRefreshing = false;
        btn.innerHTML = '▶️ Auto Refresh';
        btn.classList.remove('btn-warning');
        btn.classList.add('btn-primary');
    } else {
        autoRefreshInterval = setInterval(refreshAllData, 30000); // 30 seconds
        isAutoRefreshing = true;
        btn.innerHTML = '⏸️ Stop Auto Refresh';
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-warning');
    }
}

function exportAnalyticsData() {
    // Implementation for data export
    alert('Analytics data export feature coming soon!');
}

function generateReport() {
    // Implementation for report generation
    alert('Report generation feature coming soon!');
}

function openMonitoring() {
    window.open('{% url "admin_panel:admin_monitoring_dashboard" %}', '_blank');
}
</script>
{% endblock %}
