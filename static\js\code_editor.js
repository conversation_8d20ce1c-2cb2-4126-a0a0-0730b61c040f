/**
 * Code Editor for Programming Questions
 * Provides syntax highlighting, code execution, and auto-grading
 */

class CodeEditor {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            language: options.language || 'python',
            theme: options.theme || 'dark',
            readOnly: options.readOnly || false,
            showLineNumbers: options.showLineNumbers !== false,
            ...options
        };
        
        this.code = options.initialCode || '';
        this.testResults = [];
        this.isExecuting = false;
        
        this.init();
    }
    
    init() {
        this.createEditor();
        this.setupEventListeners();
    }
    
    createEditor() {
        this.container.innerHTML = `
            <div class="code-editor-container">
                <div class="code-editor-header">
                    <div class="code-editor-tabs">
                        <div class="code-tab active" data-tab="editor">
                            <span class="tab-icon">💻</span>
                            <span>Code Editor</span>
                        </div>
                        <div class="code-tab" data-tab="tests">
                            <span class="tab-icon">🧪</span>
                            <span>Test Results</span>
                        </div>
                    </div>
                    <div class="code-editor-actions">
                        <button class="btn-code-action" id="runCodeBtn">
                            <span class="action-icon">▶️</span>
                            Run Code
                        </button>
                        <button class="btn-code-action" id="submitCodeBtn">
                            <span class="action-icon">✅</span>
                            Submit
                        </button>
                    </div>
                </div>
                
                <div class="code-editor-content">
                    <div class="code-tab-content active" id="editorTab">
                        <div class="code-editor-wrapper">
                            <div class="line-numbers" id="lineNumbers"></div>
                            <textarea 
                                class="code-textarea" 
                                id="codeTextarea"
                                placeholder="// Write your code here..."
                                spellcheck="false"
                            >${this.code}</textarea>
                        </div>
                    </div>
                    
                    <div class="code-tab-content" id="testsTab">
                        <div class="test-results-container" id="testResults">
                            <div class="test-placeholder">
                                <div class="placeholder-icon">🧪</div>
                                <p>Run your code to see test results</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="code-editor-output" id="codeOutput">
                    <div class="output-header">
                        <span class="output-title">📋 Output</span>
                        <button class="btn-clear-output" id="clearOutputBtn">Clear</button>
                    </div>
                    <div class="output-content" id="outputContent">
                        <div class="output-placeholder">Output will appear here...</div>
                    </div>
                </div>
            </div>
        `;
        
        this.textarea = this.container.querySelector('#codeTextarea');
        this.lineNumbers = this.container.querySelector('#lineNumbers');
        this.outputContent = this.container.querySelector('#outputContent');
        
        this.updateLineNumbers();
        this.applySyntaxHighlighting();
    }
    
    setupEventListeners() {
        // Tab switching
        this.container.querySelectorAll('.code-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
        });
        
        // Code execution
        this.container.querySelector('#runCodeBtn').addEventListener('click', () => this.runCode());
        this.container.querySelector('#submitCodeBtn').addEventListener('click', () => this.submitCode());
        this.container.querySelector('#clearOutputBtn').addEventListener('click', () => this.clearOutput());
        
        // Textarea events
        this.textarea.addEventListener('input', () => {
            this.updateLineNumbers();
            this.code = this.textarea.value;
        });
        
        this.textarea.addEventListener('scroll', () => {
            this.lineNumbers.scrollTop = this.textarea.scrollTop;
        });
        
        // Tab key handling
        this.textarea.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = this.textarea.selectionStart;
                const end = this.textarea.selectionEnd;
                
                this.textarea.value = this.textarea.value.substring(0, start) + 
                                    '    ' + 
                                    this.textarea.value.substring(end);
                
                this.textarea.selectionStart = this.textarea.selectionEnd = start + 4;
                this.updateLineNumbers();
            }
        });
    }
    
    switchTab(tabName) {
        // Update tab buttons
        this.container.querySelectorAll('.code-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // Update tab content
        this.container.querySelectorAll('.code-tab-content').forEach(content => {
            content.classList.toggle('active', content.id === tabName + 'Tab');
        });
    }
    
    updateLineNumbers() {
        const lines = this.textarea.value.split('\n');
        const lineNumbersHtml = lines.map((_, index) => 
            `<div class="line-number">${index + 1}</div>`
        ).join('');
        
        this.lineNumbers.innerHTML = lineNumbersHtml;
    }
    
    applySyntaxHighlighting() {
        // Basic syntax highlighting for Python
        if (this.options.language === 'python') {
            this.textarea.classList.add('python-syntax');
        }
    }
    
    async runCode() {
        if (this.isExecuting) return;
        
        this.isExecuting = true;
        const runBtn = this.container.querySelector('#runCodeBtn');
        const originalText = runBtn.innerHTML;
        
        runBtn.innerHTML = '<span class="action-icon">⏳</span> Running...';
        runBtn.disabled = true;
        
        try {
            const response = await fetch('/api/execute-code/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    code: this.textarea.value,
                    language: this.options.language,
                    test_cases: this.options.testCases || []
                })
            });
            
            const result = await response.json();
            this.displayExecutionResult(result);
            
        } catch (error) {
            this.displayError('Execution failed: ' + error.message);
        } finally {
            this.isExecuting = false;
            runBtn.innerHTML = originalText;
            runBtn.disabled = false;
        }
    }
    
    async submitCode() {
        if (this.isExecuting) return;
        
        // First run the code to get test results
        await this.runCode();
        
        // Then submit the answer
        if (this.options.onSubmit) {
            this.options.onSubmit(this.textarea.value, this.testResults);
        }
    }
    
    displayExecutionResult(result) {
        // Display output
        this.outputContent.innerHTML = `
            <div class="execution-result">
                <div class="execution-status ${result.success ? 'success' : 'error'}">
                    <span class="status-icon">${result.success ? '✅' : '❌'}</span>
                    <span>Execution ${result.success ? 'Successful' : 'Failed'}</span>
                    <span class="execution-time">(${result.execution_time?.toFixed(3)}s)</span>
                </div>
                
                ${result.output ? `
                    <div class="output-section">
                        <h4>Output:</h4>
                        <pre class="output-text">${result.output}</pre>
                    </div>
                ` : ''}
                
                ${result.errors ? `
                    <div class="error-section">
                        <h4>Errors:</h4>
                        <pre class="error-text">${result.errors}</pre>
                    </div>
                ` : ''}
            </div>
        `;
        
        // Display test results
        if (result.test_results && result.test_results.length > 0) {
            this.displayTestResults(result.test_results, result.score);
            this.testResults = result.test_results;
        }
    }
    
    displayTestResults(testResults, score) {
        const testsContainer = this.container.querySelector('#testResults');
        
        const testResultsHtml = `
            <div class="test-results-header">
                <h3>Test Results</h3>
                <div class="test-score">
                    <span class="score-label">Score:</span>
                    <span class="score-value ${score >= 70 ? 'passing' : 'failing'}">${score.toFixed(1)}%</span>
                </div>
            </div>
            
            <div class="test-cases">
                ${testResults.map((test, index) => `
                    <div class="test-case ${test.passed ? 'passed' : 'failed'}">
                        <div class="test-header">
                            <span class="test-icon">${test.passed ? '✅' : '❌'}</span>
                            <span class="test-title">Test Case ${index + 1}</span>
                        </div>
                        
                        ${test.input ? `
                            <div class="test-detail">
                                <strong>Input:</strong> <code>${test.input}</code>
                            </div>
                        ` : ''}
                        
                        <div class="test-detail">
                            <strong>Expected:</strong> <code>${test.expected}</code>
                        </div>
                        
                        <div class="test-detail">
                            <strong>Actual:</strong> <code>${test.actual}</code>
                        </div>
                        
                        ${test.error ? `
                            <div class="test-error">
                                <strong>Error:</strong> ${test.error}
                            </div>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        `;
        
        testsContainer.innerHTML = testResultsHtml;
        
        // Switch to tests tab to show results
        this.switchTab('tests');
    }
    
    displayError(message) {
        this.outputContent.innerHTML = `
            <div class="execution-error">
                <span class="error-icon">❌</span>
                <span>${message}</span>
            </div>
        `;
    }
    
    clearOutput() {
        this.outputContent.innerHTML = '<div class="output-placeholder">Output will appear here...</div>';
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
    
    getValue() {
        return this.textarea.value;
    }
    
    setValue(code) {
        this.textarea.value = code;
        this.code = code;
        this.updateLineNumbers();
    }
}
