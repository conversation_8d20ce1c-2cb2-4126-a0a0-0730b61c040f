/* WCAG 2.1 Accessibility Styles */

/* Screen Reader Only Content */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Skip Links */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary);
    color: black;
    padding: 8px 12px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
    font-weight: bold;
    font-size: 14px;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 6px;
}

/* Focus Indicators */
.focus-visible:focus,
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
a:focus-visible,
[tabindex]:focus-visible {
    outline: 3px solid var(--primary) !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3) !important;
}

/* Remove default focus outline for mouse users */
button:focus:not(:focus-visible),
input:focus:not(:focus-visible),
select:focus:not(:focus-visible),
textarea:focus:not(:focus-visible),
a:focus:not(:focus-visible),
[tabindex]:focus:not(:focus-visible) {
    outline: none;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --primary: #ffff00;
        --text-primary: #ffffff;
        --text-secondary: #ffffff;
        --background: #000000;
        --dark-card: #000000;
        --border-color: #ffffff;
    }
    
    .terminal-window,
    .admin-card,
    .form-input,
    .btn {
        border: 2px solid #ffffff !important;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .matrix-rain,
    .floating-particles,
    .typing-animation {
        animation: none !important;
    }
}

/* Form Accessibility */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.required-indicator {
    color: #ff6b6b;
    font-weight: bold;
    margin-left: 0.25rem;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
}

.form-input:invalid,
.form-textarea:invalid,
.form-select:invalid {
    border-color: #ff6b6b;
}

.error-message {
    color: #ff6b6b;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-message::before {
    content: "⚠️";
    font-size: 1rem;
}

.success-message {
    color: #51cf66;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.success-message::before {
    content: "✅";
    font-size: 1rem;
}

/* Button Accessibility */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid transparent;
    border-radius: 0.5rem;
    background: var(--primary);
    color: black;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px; /* WCAG minimum touch target size */
    min-width: 44px;
}

.btn:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    background: var(--primary);
    transform: none;
    box-shadow: none;
}

/* Link Accessibility */
a {
    color: var(--primary);
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 2px;
}

a:hover {
    text-decoration-thickness: 3px;
}

a:visited {
    color: #c084fc; /* Distinct visited link color */
}

/* Table Accessibility */
.accessible-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.accessible-table th,
.accessible-table td {
    padding: 0.75rem;
    text-align: left;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.accessible-table th {
    background: rgba(255, 215, 0, 0.1);
    font-weight: 600;
    color: var(--text-primary);
}

.accessible-table caption {
    caption-side: top;
    padding: 0.75rem;
    font-weight: 600;
    text-align: left;
    color: var(--text-primary);
}

/* Modal Accessibility */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--dark-card);
    border-radius: 0.75rem;
    border: 2px solid var(--primary);
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
    min-width: 44px;
    min-height: 44px;
}

/* Progress Indicators */
.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--primary);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.8rem;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.9rem;
    font-weight: 600;
}

.status-success {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-warning {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.status-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.status-info {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

/* Landmark Roles */
[role="banner"],
[role="navigation"],
[role="main"],
[role="complementary"],
[role="contentinfo"] {
    position: relative;
}

/* Live Regions */
[aria-live="polite"],
[aria-live="assertive"] {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Keyboard Navigation Helpers */
.keyboard-shortcuts {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    background: var(--dark-card);
    border: 2px solid var(--primary);
    border-radius: 0.5rem;
    padding: 1rem;
    max-width: 300px;
    z-index: 999;
    display: none;
}

.keyboard-shortcuts.show {
    display: block;
}

.keyboard-shortcuts h3 {
    margin: 0 0 1rem 0;
    color: var(--primary);
    font-size: 1rem;
}

.keyboard-shortcuts dl {
    margin: 0;
}

.keyboard-shortcuts dt {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.keyboard-shortcuts dd {
    color: var(--text-secondary);
    margin: 0 0 0.75rem 0;
    font-size: 0.9rem;
}

/* Print Styles */
@media print {
    .skip-link,
    .keyboard-shortcuts,
    .floating-particles,
    .matrix-rain {
        display: none !important;
    }
    
    * {
        background: white !important;
        color: black !important;
    }
    
    a {
        text-decoration: underline;
    }
    
    a[href^="http"]:after {
        content: " (" attr(href) ")";
    }
}
