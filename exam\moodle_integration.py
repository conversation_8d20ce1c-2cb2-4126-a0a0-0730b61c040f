"""
Moodle LMS Integration
Provides integration with Moodle 4+ for grade passback, user sync, and course connectivity
"""

import requests
import json
import hashlib
import hmac
from datetime import datetime
from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone
from .models import Player, ExamInstance, TestVersion
import logging

logger = logging.getLogger(__name__)


class MoodleConnector:
    """Main Moodle integration class"""
    
    def __init__(self, moodle_url=None, token=None):
        self.moodle_url = moodle_url or getattr(settings, 'MOODLE_URL', '')
        self.token = token or getattr(settings, 'MOODLE_TOKEN', '')
        self.api_endpoint = f"{self.moodle_url}/webservice/rest/server.php"
        
        if not self.moodle_url or not self.token:
            logger.warning("Moodle integration not configured - missing URL or token")
    
    def _make_request(self, function, params=None):
        """Make API request to <PERSON>od<PERSON>"""
        
        if not self.moodle_url or not self.token:
            return {'error': 'Moodle not configured'}
        
        data = {
            'wstoken': self.token,
            'wsfunction': function,
            'moodlewsrestformat': 'json'
        }
        
        if params:
            data.update(params)
        
        try:
            response = requests.post(self.api_endpoint, data=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if 'exception' in result:
                logger.error(f"Moodle API error: {result['exception']}")
                return {'error': result['message']}
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Moodle API request failed: {str(e)}")
            return {'error': str(e)}
        except json.JSONDecodeError as e:
            logger.error(f"Moodle API response parsing failed: {str(e)}")
            return {'error': 'Invalid response format'}
    
    def test_connection(self):
        """Test connection to Moodle"""
        
        result = self._make_request('core_webservice_get_site_info')
        
        if 'error' in result:
            return {
                'success': False,
                'error': result['error']
            }
        
        return {
            'success': True,
            'site_name': result.get('sitename', 'Unknown'),
            'moodle_version': result.get('release', 'Unknown'),
            'user_count': result.get('usercount', 0)
        }
    
    def get_courses(self):
        """Get list of courses from Moodle"""
        
        result = self._make_request('core_course_get_courses')
        
        if 'error' in result:
            return {'error': result['error']}
        
        courses = []
        for course in result:
            if course.get('id', 0) > 1:  # Skip site course (id=1)
                courses.append({
                    'id': course['id'],
                    'name': course['fullname'],
                    'shortname': course['shortname'],
                    'category': course.get('categoryname', 'Unknown'),
                    'enrolled_users': course.get('enrolledusercount', 0),
                    'start_date': course.get('startdate', 0),
                    'end_date': course.get('enddate', 0)
                })
        
        return {'courses': courses}
    
    def get_course_users(self, course_id):
        """Get enrolled users for a specific course"""
        
        result = self._make_request('core_enrol_get_enrolled_users', {
            'courseid': course_id
        })
        
        if 'error' in result:
            return {'error': result['error']}
        
        users = []
        for user in result:
            users.append({
                'id': user['id'],
                'username': user['username'],
                'firstname': user['firstname'],
                'lastname': user['lastname'],
                'email': user['email'],
                'roles': [role['shortname'] for role in user.get('roles', [])]
            })
        
        return {'users': users}
    
    def create_gradebook_item(self, course_id, item_name, max_grade=100):
        """Create a gradebook item in Moodle"""
        
        result = self._make_request('core_grades_create_gradecategory', {
            'courseid': course_id,
            'fullname': item_name,
            'aggregation': 0,  # Mean of grades
        })
        
        if 'error' in result:
            return {'error': result['error']}
        
        return {
            'success': True,
            'category_id': result.get('id'),
            'message': f'Gradebook item "{item_name}" created successfully'
        }
    
    def send_grade(self, course_id, user_id, grade_item, grade_value, max_grade=100):
        """Send grade to Moodle gradebook"""
        
        # Normalize grade to percentage
        grade_percentage = (grade_value / max_grade) * 100 if max_grade > 0 else 0
        
        result = self._make_request('core_grades_update_grades', {
            'source': 'kernelios_exam_system',
            'courseid': course_id,
            'component': 'mod_assign',  # Using assignment component
            'activityid': grade_item,
            'itemnumber': 0,
            'grades': json.dumps([{
                'studentid': user_id,
                'grade': grade_percentage,
                'feedback': f'KERNELiOS Exam Score: {grade_value}/{max_grade}'
            }])
        })
        
        if 'error' in result:
            return {'error': result['error']}
        
        return {
            'success': True,
            'message': f'Grade {grade_value}/{max_grade} sent successfully'
        }
    
    def sync_user_from_moodle(self, moodle_user_id):
        """Sync a user from Moodle to local system"""
        
        result = self._make_request('core_user_get_users_by_field', {
            'field': 'id',
            'values[0]': moodle_user_id
        })
        
        if 'error' in result or not result:
            return {'error': 'User not found in Moodle'}
        
        moodle_user = result[0]
        
        try:
            # Check if user already exists
            user, created = User.objects.get_or_create(
                username=moodle_user['username'],
                defaults={
                    'email': moodle_user['email'],
                    'first_name': moodle_user['firstname'],
                    'last_name': moodle_user['lastname'],
                    'is_active': True
                }
            )
            
            if not created:
                # Update existing user
                user.email = moodle_user['email']
                user.first_name = moodle_user['firstname']
                user.last_name = moodle_user['lastname']
                user.save()
            
            return {
                'success': True,
                'user_id': user.id,
                'created': created,
                'message': f'User {user.username} {"created" if created else "updated"} successfully'
            }
            
        except Exception as e:
            logger.error(f"Error syncing user from Moodle: {str(e)}")
            return {'error': str(e)}
    
    def bulk_sync_course_users(self, course_id):
        """Sync all users from a Moodle course"""
        
        course_users = self.get_course_users(course_id)
        
        if 'error' in course_users:
            return course_users
        
        sync_results = {
            'total_users': len(course_users['users']),
            'created': 0,
            'updated': 0,
            'errors': 0,
            'details': []
        }
        
        for moodle_user in course_users['users']:
            result = self.sync_user_from_moodle(moodle_user['id'])
            
            if result.get('success'):
                if result.get('created'):
                    sync_results['created'] += 1
                else:
                    sync_results['updated'] += 1
                
                sync_results['details'].append({
                    'username': moodle_user['username'],
                    'status': 'success',
                    'action': 'created' if result.get('created') else 'updated'
                })
            else:
                sync_results['errors'] += 1
                sync_results['details'].append({
                    'username': moodle_user['username'],
                    'status': 'error',
                    'error': result.get('error', 'Unknown error')
                })
        
        return sync_results


class MoodleGradePassback:
    """Handle grade passback to Moodle"""
    
    def __init__(self, connector=None):
        self.connector = connector or MoodleConnector()
    
    def send_exam_grade(self, player, course_id, grade_item_id):
        """Send exam grade to Moodle"""
        
        if not player.end_time:
            return {'error': 'Exam not completed yet'}
        
        # Get Moodle user ID (this would need to be stored in user profile)
        moodle_user_id = self._get_moodle_user_id(player.user)
        
        if not moodle_user_id:
            return {'error': 'User not linked to Moodle account'}
        
        # Calculate grade
        max_score = player.instance.version.max_score or 100
        grade_value = player.score or 0
        
        result = self.connector.send_grade(
            course_id=course_id,
            user_id=moodle_user_id,
            grade_item=grade_item_id,
            grade_value=grade_value,
            max_grade=max_score
        )
        
        if result.get('success'):
            # Log successful grade passback
            logger.info(f"Grade passback successful: {player.user.username} - {grade_value}/{max_score}")
        
        return result
    
    def _get_moodle_user_id(self, user):
        """Get Moodle user ID for a Django user"""
        # This would typically be stored in a user profile or custom field
        # For now, return None - this would need to be implemented based on your user model
        return None
    
    def bulk_send_instance_grades(self, instance_id, course_id, grade_item_id):
        """Send grades for all completed players in an instance"""
        
        try:
            instance = ExamInstance.objects.get(id=instance_id)
            completed_players = Player.objects.filter(
                instance=instance,
                end_time__isnull=False
            )
            
            results = {
                'total_players': completed_players.count(),
                'successful': 0,
                'failed': 0,
                'details': []
            }
            
            for player in completed_players:
                result = self.send_exam_grade(player, course_id, grade_item_id)
                
                if result.get('success'):
                    results['successful'] += 1
                    results['details'].append({
                        'username': player.user.username,
                        'grade': player.score,
                        'status': 'success'
                    })
                else:
                    results['failed'] += 1
                    results['details'].append({
                        'username': player.user.username,
                        'status': 'failed',
                        'error': result.get('error', 'Unknown error')
                    })
            
            return results
            
        except ExamInstance.DoesNotExist:
            return {'error': 'Exam instance not found'}
        except Exception as e:
            logger.error(f"Error in bulk grade passback: {str(e)}")
            return {'error': str(e)}


class MoodleWebhookHandler:
    """Handle webhooks from Moodle"""
    
    def __init__(self, secret_key=None):
        self.secret_key = secret_key or getattr(settings, 'MOODLE_WEBHOOK_SECRET', '')
    
    def verify_webhook_signature(self, payload, signature):
        """Verify webhook signature"""
        
        if not self.secret_key:
            logger.warning("Moodle webhook secret not configured")
            return False
        
        expected_signature = hmac.new(
            self.secret_key.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(signature, expected_signature)
    
    def handle_user_enrolled(self, data):
        """Handle user enrollment webhook"""
        
        course_id = data.get('courseid')
        user_id = data.get('userid')
        
        if not course_id or not user_id:
            return {'error': 'Missing required data'}
        
        # Sync the enrolled user
        connector = MoodleConnector()
        result = connector.sync_user_from_moodle(user_id)
        
        return result
    
    def handle_grade_updated(self, data):
        """Handle grade update webhook"""
        
        # This could trigger actions in the exam system
        # For example, updating local records or sending notifications
        
        return {'success': True, 'message': 'Grade update processed'}


# Global instances
moodle_connector = MoodleConnector()
moodle_grade_passback = MoodleGradePassback()
moodle_webhook_handler = MoodleWebhookHandler()
