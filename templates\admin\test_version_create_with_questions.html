{% extends 'admin/base_admin.html' %}

{% block title %}{{ version.name }} - Add Questions{% endblock %}

{% block content %}
{% csrf_token %}

<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">{{ version.name|lower }}_builder.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    ✨ {{ version.name }} - Question Builder
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > Add questions to your newly created test version
                </p>
            </div>
            <div style="display: flex; gap: 1rem;">
                <button onclick="showQuestionBuilder()" class="btn btn-primary">
                    ➕ Add Question
                </button>
                <a href="{% url 'admin_panel:admin_test_versions' %}" class="btn btn-secondary">
                    ✅ Finish & View All Versions
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Score Distribution -->
<div class="admin-grid admin-grid-4">
    <div class="admin-card">
        <h3>📊 Total Questions</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ questions.count }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Normal: {{ score_distribution.normal_count }} | Bonus: {{ score_distribution.bonus_count }}
        </p>
    </div>
    
    <div class="admin-card">
        <h3>🎯 Score Distribution</h3>
        <div style="font-size: 2rem; color: {% if score_distribution.is_balanced %}var(--success){% else %}var(--warning){% endif %}; font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ score_distribution.normal_total }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Normal points (target: 100)
        </p>
    </div>
    
    <div class="admin-card">
        <h3>🎁 Bonus Points</h3>
        <div style="font-size: 2rem; color: var(--accent); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ score_distribution.bonus_total }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Extra credit available
        </p>
    </div>
    
    <div class="admin-card">
        <h3>⚡ Quick Actions</h3>
        <div style="display: grid; gap: 0.5rem; margin-top: 1rem;">
            <button onclick="addTemplateQuestions()" class="btn btn-secondary">
                📋 Add Templates
            </button>
            <button onclick="showImportModal()" class="btn btn-secondary">
                📥 Import CSV
            </button>
        </div>
    </div>
</div>

<!-- Questions List -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">question_list.json</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <h3 style="color: var(--primary);">📝 Questions ({{ questions.count }})</h3>
            {% if questions.count == 0 %}
            <div style="color: var(--text-muted); font-style: italic;">
                No questions yet - start building your exam!
            </div>
            {% endif %}
        </div>
        
        {% if questions %}
            <div id="questionsList" style="display: grid; gap: 1rem;">
                {% for question in questions %}
                <div class="question-item" style="background: var(--dark-card); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 0.75rem; padding: 1.5rem; border-left: 4px solid {% if question.is_bonus %}var(--accent){% else %}var(--primary){% endif %};">
                    
                    <!-- Question Header -->
                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                                <h4 style="margin: 0; color: var(--text-primary); font-family: 'JetBrains Mono', monospace;">
                                    {{ question.order }}. {{ question.name }}
                                </h4>
                                <span style="background: {% if question.question_type == 'text' %}var(--primary){% elif question.question_type == 'multiple_choice' %}var(--success){% else %}var(--warning){% endif %}; color: var(--dark); padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem; font-weight: bold;">
                                    {{ question.get_question_type_display_icon }} {{ question.get_question_type_display }}
                                </span>
                                {% if question.is_bonus %}
                                    <span style="background: var(--accent); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem; font-weight: bold;">
                                        🎁 BONUS
                                    </span>
                                {% endif %}
                            </div>
                            <p style="color: var(--text-secondary); margin: 0; font-size: 0.9rem;">
                                {{ question.description|truncatechars:100 }}
                            </p>
                        </div>
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <span style="color: var(--primary); font-weight: bold; font-family: 'JetBrains Mono', monospace; font-size: 1.1rem;">
                                {{ question.score }} pts
                            </span>
                            <div style="display: flex; gap: 0.5rem;">
                                <button onclick="editQuestion({{ question.id }})" class="btn btn-secondary" style="padding: 0.5rem;">
                                    ✏️
                                </button>
                                <button onclick="deleteQuestion({{ question.id }})" class="btn btn-danger" style="padding: 0.5rem;">
                                    🗑️
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Question Preview -->
                    <div style="padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; margin-top: 1rem;">
                        <strong style="color: var(--text-secondary);">Preview:</strong>
                        <div style="color: var(--text-primary); margin-top: 0.5rem;">{{ question.description }}</div>
                        
                        {% if question.question_type == 'multiple_choice' and question.choices %}
                        <div style="margin-top: 0.5rem; display: grid; gap: 0.25rem;">
                            {% for key, value in question.choices.items %}
                            <div style="padding: 0.25rem; {% if key == question.correct_answer %}color: var(--success); font-weight: bold;{% else %}color: var(--text-muted);{% endif %}">
                                {{ key }}: {{ value }} {% if key == question.correct_answer %}✓{% endif %}
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div style="margin-top: 0.5rem;">
                            <strong style="color: var(--success);">Answer:</strong> 
                            <code style="background: rgba(0, 255, 65, 0.1); color: var(--success); padding: 0.25rem; border-radius: 0.25rem;">{{ question.correct_answer }}</code>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div style="text-align: center; padding: 4rem 2rem; color: var(--text-muted);">
                <div style="font-size: 4rem; margin-bottom: 1rem;">🚀</div>
                <h3 style="color: var(--text-secondary); margin-bottom: 1rem;">Ready to Build Your Exam!</h3>
                <p style="margin-bottom: 2rem;">Start by adding your first question to {{ version.name }}.</p>
                <button onclick="showQuestionBuilder()" class="btn btn-primary">
                    ➕ Add First Question
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Question Builder Modal -->
<div id="questionBuilderModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 2000; align-items: center; justify-content: center;">
    <div class="terminal-window" style="width: 95%; max-width: 1000px; margin: 0; max-height: 95vh; overflow-y: auto;">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">question_builder.py</div>
            <button onclick="closeQuestionBuilder()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.2rem;">×</button>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">🔧 Add Question to {{ version.name }}</h3>
            
            <form id="questionBuilderForm" method="post" action="{% url 'admin_panel:admin_create_test_version' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="add_question">
                <input type="hidden" name="version_id" value="{{ version.id }}">
                
                <!-- Include the same question builder interface from test_version_detail.html -->
                <!-- Question Type Selection -->
                <div class="form-group">
                    <label class="form-label">📝 Question Type</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent; transition: all 0.3s ease;" class="question-type-option" data-type="text">
                            <input type="radio" name="question_type" value="text" checked style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">📝 Text Answer</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Single correct text answer</div>
                            </div>
                        </label>
                        
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent; transition: all 0.3s ease;" class="question-type-option" data-type="multiple_choice">
                            <input type="radio" name="question_type" value="multiple_choice" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">🔘 Multiple Choice</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">4 options with one correct</div>
                            </div>
                        </label>
                        
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent; transition: all 0.3s ease;" class="question-type-option" data-type="true_false">
                            <input type="radio" name="question_type" value="true_false" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">✅ True/False</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Simple boolean question</div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- Basic Question Info -->
                <div class="admin-grid admin-grid-2">
                    <div class="form-group">
                        <label class="form-label">🏷️ Question Title *</label>
                        <input type="text" name="name" class="form-input" placeholder="e.g., Port Scanning Basics" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">📊 Question Order</label>
                        <input type="number" name="order" class="form-input" value="{{ questions.count|add:1 }}" min="1" required>
                    </div>
                </div>
                
                <!-- Question Description -->
                <div class="form-group">
                    <label class="form-label">❓ Question Text *</label>
                    <textarea name="description" class="form-textarea" rows="4" placeholder="Enter the full question text here..." required></textarea>
                </div>
                
                <!-- Dynamic Answer Section -->
                <div id="answerSection">
                    <!-- Text Answer Section -->
                    <div id="textAnswerSection" class="answer-section">
                        <div class="form-group">
                            <label class="form-label">✅ Correct Answer *</label>
                            <input type="text" name="correct_answer_text" class="form-input" placeholder="Enter the correct answer">
                        </div>
                    </div>
                    
                    <!-- Multiple Choice Section -->
                    <div id="multipleChoiceSection" class="answer-section" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">🔘 Answer Choices</label>
                            <div style="display: grid; gap: 1rem;">
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <input type="radio" name="correct_choice" value="A" style="margin: 0;">
                                    <strong style="color: var(--primary); min-width: 20px;">A:</strong>
                                    <input type="text" name="choice_a" class="form-input" placeholder="Option A" style="flex: 1;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <input type="radio" name="correct_choice" value="B" style="margin: 0;">
                                    <strong style="color: var(--primary); min-width: 20px;">B:</strong>
                                    <input type="text" name="choice_b" class="form-input" placeholder="Option B" style="flex: 1;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <input type="radio" name="correct_choice" value="C" style="margin: 0;">
                                    <strong style="color: var(--primary); min-width: 20px;">C:</strong>
                                    <input type="text" name="choice_c" class="form-input" placeholder="Option C" style="flex: 1;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <input type="radio" name="correct_choice" value="D" style="margin: 0;">
                                    <strong style="color: var(--primary); min-width: 20px;">D:</strong>
                                    <input type="text" name="choice_d" class="form-input" placeholder="Option D" style="flex: 1;">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- True/False Section -->
                    <div id="trueFalseSection" class="answer-section" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">✅ Correct Answer</label>
                            <div style="display: flex; gap: 2rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="radio" name="correct_tf" value="A" style="margin: 0;">
                                    <span style="color: var(--success);">True</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="radio" name="correct_tf" value="B" style="margin: 0;">
                                    <span style="color: var(--error);">False</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Options -->
                <div class="admin-grid admin-grid-2">
                    <div class="form-group">
                        <label class="form-label">🎁 Question Type</label>
                        <div style="display: flex; gap: 2rem;">
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="radio" name="is_bonus" value="false" checked style="margin: 0;">
                                <span style="color: var(--text-primary);">📝 Normal Question</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="radio" name="is_bonus" value="true" style="margin: 0;">
                                <span style="color: var(--accent);">🎁 Bonus Question</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">🎯 Points</label>
                        <input type="number" name="score" class="form-input" value="5" min="1" max="50">
                        <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                            Will be auto-calculated to total 100 points
                        </small>
                    </div>
                </div>
                
                <!-- Optional Explanation -->
                <div class="form-group">
                    <label class="form-label">💡 Explanation (Optional)</label>
                    <textarea name="explanation" class="form-textarea" rows="3" placeholder="Optional explanation shown after answering..."></textarea>
                </div>
                
                <!-- Action Buttons -->
                <div style="display: flex; gap: 1rem; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        ✨ Add Question
                    </button>
                    <button type="button" onclick="closeQuestionBuilder()" class="btn btn-secondary">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Include the same JavaScript from test_version_detail.html for question builder functionality
function showQuestionBuilder() {
    document.getElementById('questionBuilderForm').reset();
    showAnswerSection('text');
    updateQuestionTypeSelection('text');
    document.getElementById('questionBuilderModal').style.display = 'flex';
}

function closeQuestionBuilder() {
    document.getElementById('questionBuilderModal').style.display = 'none';
}

// Question type selection handling
document.querySelectorAll('input[name="question_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        showAnswerSection(this.value);
        updateQuestionTypeSelection(this.value);
    });
});

function showAnswerSection(type) {
    document.querySelectorAll('.answer-section').forEach(section => {
        section.style.display = 'none';
    });
    
    const sectionMap = {
        'text': 'textAnswerSection',
        'multiple_choice': 'multipleChoiceSection',
        'true_false': 'trueFalseSection'
    };
    
    const sectionId = sectionMap[type];
    if (sectionId) {
        document.getElementById(sectionId).style.display = 'block';
    }
}

function updateQuestionTypeSelection(type) {
    document.querySelectorAll('.question-type-option').forEach(option => {
        option.style.borderColor = 'transparent';
        option.style.background = 'rgba(255, 255, 255, 0.05)';
    });
    
    const selectedOption = document.querySelector(`.question-type-option[data-type="${type}"]`);
    if (selectedOption) {
        selectedOption.style.borderColor = 'var(--primary)';
        selectedOption.style.background = 'rgba(255, 215, 0, 0.1)';
    }
}

function editQuestion(questionId) {
    console.log('Edit question:', questionId);
}

function deleteQuestion(questionId) {
    if (confirm('Are you sure you want to delete this question?')) {
        console.log('Delete question:', questionId);
    }
}

function addTemplateQuestions() {
    if (confirm('Add template questions to get started quickly?')) {
        console.log('Adding template questions...');
    }
}

function showImportModal() {
    console.log('Show import modal');
}

// Close modal on outside click
document.getElementById('questionBuilderModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuestionBuilder();
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateQuestionTypeSelection('text');
});
</script>
{% endblock %}
