{% extends 'admin/base_admin.html' %}

{% block title %}User Management - KERNELiOS Admin{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">user_management.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    👥 User Management
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > Manage teachers, students, and system access
                </p>
            </div>
            <button onclick="showCreateUserModal()" class="btn btn-primary">
                ➕ Create User
            </button>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="admin-grid admin-grid-4">
    <div class="admin-card">
        <h3>👨‍🏫 Teachers</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ users|length|add:"-1" }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Staff members</p>
    </div>
    
    <div class="admin-card">
        <h3>👨‍🎓 Students</h3>
        <div style="font-size: 2rem; color: var(--success); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ users|length }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Regular users</p>
    </div>
    
    <div class="admin-card">
        <h3>🎯 Active Players</h3>
        <div style="font-size: 2rem; color: var(--warning); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            0
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Currently in exams</p>
    </div>
    
    <div class="admin-card">
        <h3>📅 Recent Logins</h3>
        <div style="font-size: 2rem; color: var(--accent); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            0
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Last 24 hours</p>
    </div>
</div>

<!-- User List -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">user_list.json</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <h3 style="color: var(--primary);">👤 All Users</h3>
            <div style="display: flex; gap: 1rem; align-items: center;">
                <select id="userTypeFilter" class="form-select" style="width: auto;">
                    <option value="">All Users</option>
                    <option value="staff">Teachers Only</option>
                    <option value="student">Students Only</option>
                    <option value="active">Active Only</option>
                </select>
                <input type="text" id="userSearch" class="form-input" placeholder="Search users..." style="width: 200px;">
            </div>
        </div>
        
        {% if users %}
            <div class="admin-table-container" style="overflow-x: auto;">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Exam Activity</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        {% for user in users %}
                        <tr class="user-row" data-type="{% if user.is_staff %}staff{% else %}student{% endif %}" data-active="{% if user.is_active %}true{% else %}false{% endif %}">
                            <td>
                                <div style="display: flex; align-items: center; gap: 0.75rem;">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); display: flex; align-items: center; justify-content: center; color: var(--dark); font-weight: bold;">
                                        {{ user.username|first|upper }}
                                    </div>
                                    <div>
                                        <div style="color: var(--text-primary); font-weight: 500;">{{ user.username }}</div>
                                        {% if user.email %}
                                        <div style="color: var(--text-muted); font-size: 0.8rem;">{{ user.email }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if user.is_superuser %}
                                    <span style="background: var(--error); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem;">
                                        🔑 Super Admin
                                    </span>
                                {% elif user.is_staff %}
                                    <span style="background: var(--primary); color: var(--dark); padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem;">
                                        👨‍🏫 Teacher
                                    </span>
                                {% else %}
                                    <span style="background: var(--success); color: var(--dark); padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem;">
                                        👨‍🎓 Student
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_active %}
                                    <span style="color: var(--success);">🟢 Active</span>
                                {% else %}
                                    <span style="color: var(--error);">🔴 Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <div style="font-family: 'JetBrains Mono', monospace; font-size: 0.9rem;">
                                    {% if user.player_count > 0 %}
                                        <div style="color: var(--text-primary);">{{ user.player_count }} exam{{ user.player_count|pluralize }}</div>
                                    {% else %}
                                        <div style="color: var(--text-muted);">No exams</div>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div style="font-family: 'JetBrains Mono', monospace; font-size: 0.9rem; color: var(--text-secondary);">
                                    {% if user.last_login %}
                                        {{ user.last_login|date:"M d, H:i" }}
                                    {% else %}
                                        Never
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 0.5rem;">
                                    <button onclick="editUser({{ user.id }})" class="btn btn-secondary" style="padding: 0.5rem;">
                                        ✏️
                                    </button>
                                    {% if not user.is_superuser %}
                                    <button onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|yesno:'false,true' }})" 
                                            class="btn {% if user.is_active %}btn-secondary{% else %}btn-primary{% endif %}" style="padding: 0.5rem;">
                                        {% if user.is_active %}⏸️{% else %}▶️{% endif %}
                                    </button>
                                    {% endif %}
                                    {% if user.username != request.user.username and not user.is_superuser %}
                                    <button onclick="deleteUser({{ user.id }})" class="btn btn-danger" style="padding: 0.5rem;">
                                        🗑️
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div style="text-align: center; padding: 4rem 2rem; color: var(--text-muted);">
                <div style="font-size: 4rem; margin-bottom: 1rem;">👥</div>
                <h3 style="color: var(--text-secondary); margin-bottom: 1rem;">No Users Found</h3>
                <p style="margin-bottom: 2rem;">Create your first user to get started.</p>
                <button onclick="showCreateUserModal()" class="btn btn-primary">
                    ➕ Create First User
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Create User Modal -->
<div id="createUserModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 2000; align-items: center; justify-content: center;">
    <div class="terminal-window" style="width: 90%; max-width: 600px; margin: 0;">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">create_user.py</div>
            <button onclick="closeCreateUserModal()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.2rem;">×</button>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">👤 Create New User</h3>
            
            <form id="createUserForm" method="post" action="{% url 'admin_panel:admin_create_user' %}">
                {% csrf_token %}
                
                <div class="admin-grid admin-grid-2">
                    <div class="form-group">
                        <label class="form-label">Username *</label>
                        <input type="text" name="username" class="form-input" placeholder="Enter username" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-input" placeholder="<EMAIL>">
                    </div>
                </div>
                
                <div class="admin-grid admin-grid-2">
                    <div class="form-group">
                        <label class="form-label">Password *</label>
                        <input type="password" name="password" class="form-input" placeholder="Enter password" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Confirm Password *</label>
                        <input type="password" name="password_confirm" class="form-input" placeholder="Confirm password" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">User Role</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent;">
                            <input type="radio" name="role" value="student" checked style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">👨‍🎓 Student</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Regular exam participant</div>
                            </div>
                        </label>
                        
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent;">
                            <input type="radio" name="role" value="teacher" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">👨‍🏫 Teacher</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Can create and manage exams</div>
                            </div>
                        </label>

                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent;">
                            <input type="radio" name="role" value="admin" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">👨‍💼 Admin</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Full system administration access</div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        ✨ Create User
                    </button>
                    <button type="button" onclick="closeCreateUserModal()" class="btn btn-secondary">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div id="editUserModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 2000; align-items: center; justify-content: center;">
    <div class="terminal-window" style="width: 90%; max-width: 600px; margin: 0;">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">edit_user.py</div>
            <button onclick="closeEditUserModal()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.2rem;">×</button>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">✏️ Edit User</h3>

            <form id="editUserForm" method="post" action="{% url 'admin_panel:admin_edit_user' %}">
                {% csrf_token %}
                <input type="hidden" id="editUserId" name="user_id">

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                    <div>
                        <label style="display: block; color: var(--text-primary); margin-bottom: 0.5rem; font-weight: 600;">
                            Username *
                        </label>
                        <input type="text" id="editUsername" name="username" required
                               style="width: 100%; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border: 2px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem; color: var(--text-primary);">
                    </div>

                    <div>
                        <label style="display: block; color: var(--text-primary); margin-bottom: 0.5rem; font-weight: 600;">
                            Email *
                        </label>
                        <input type="email" id="editEmail" name="email" required
                               style="width: 100%; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border: 2px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem; color: var(--text-primary);">
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                    <div>
                        <label style="display: block; color: var(--text-primary); margin-bottom: 0.5rem; font-weight: 600;">
                            First Name
                        </label>
                        <input type="text" id="editFirstName" name="first_name"
                               style="width: 100%; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border: 2px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem; color: var(--text-primary);">
                    </div>

                    <div>
                        <label style="display: block; color: var(--text-primary); margin-bottom: 0.5rem; font-weight: 600;">
                            Last Name
                        </label>
                        <input type="text" id="editLastName" name="last_name"
                               style="width: 100%; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border: 2px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem; color: var(--text-primary);">
                    </div>
                </div>

                <div style="margin-bottom: 1rem;">
                    <label style="display: block; color: var(--text-primary); margin-bottom: 0.5rem; font-weight: 600;">
                        New Password (leave blank to keep current)
                    </label>
                    <input type="password" id="editPassword" name="password"
                           style="width: 100%; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border: 2px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem; color: var(--text-primary);"
                           placeholder="Enter new password or leave blank">
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                    <div>
                        <label style="display: block; color: var(--text-primary); margin-bottom: 0.5rem; font-weight: 600;">
                            User Role *
                        </label>
                        <select id="editRole" name="role" required
                                style="width: 100%; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border: 2px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem; color: var(--text-primary); font-size: 1rem;">
                            <option value="student">👨‍🎓 Student - Regular exam participant</option>
                            <option value="teacher">👨‍🏫 Teacher - Can create and manage exams</option>
                            <option value="admin">👨‍💼 Admin - Full system administration access</option>
                        </select>
                    </div>

                    <div>
                        <label style="display: block; color: var(--text-primary); margin-bottom: 0.5rem; font-weight: 600;">
                            Account Status
                        </label>
                        <label style="display: flex; align-items: center; gap: 0.75rem; color: var(--text-primary); cursor: pointer; margin-top: 0.75rem;">
                            <input type="checkbox" id="editIsActive" name="is_active" style="width: 1.2rem; height: 1.2rem;">
                            <span>✅ Active (user can log in)</span>
                        </label>
                    </div>
                </div>

                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        💾 Update User
                    </button>
                    <button type="button" onclick="closeEditUserModal()" class="btn btn-secondary">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Filter and search functionality
document.getElementById('userTypeFilter').addEventListener('change', filterUsers);
document.getElementById('userSearch').addEventListener('input', filterUsers);

function filterUsers() {
    const typeFilter = document.getElementById('userTypeFilter').value;
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const rows = document.querySelectorAll('.user-row');
    
    rows.forEach(row => {
        let show = true;
        
        // Type filter
        if (typeFilter) {
            if (typeFilter === 'active' && row.dataset.active !== 'true') {
                show = false;
            } else if (typeFilter !== 'active' && row.dataset.type !== typeFilter) {
                show = false;
            }
        }
        
        // Search filter
        if (searchTerm && show) {
            const text = row.textContent.toLowerCase();
            if (!text.includes(searchTerm)) {
                show = false;
            }
        }
        
        row.style.display = show ? '' : 'none';
    });
}

function showCreateUserModal() {
    document.getElementById('createUserModal').style.display = 'flex';
}

function closeCreateUserModal() {
    document.getElementById('createUserModal').style.display = 'none';
    document.getElementById('createUserForm').reset();
}

function closeEditUserModal() {
    document.getElementById('editUserModal').style.display = 'none';
    document.getElementById('editUserForm').reset();
}

async function editUser(userId) {
    try {
        // Fetch user data
        const response = await fetch(`{% url "admin_panel:admin_users_api" %}?action=get_user&user_id=${userId}`);
        const result = await response.json();

        if (result.success) {
            const user = result.user;

            // Populate edit form
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editUsername').value = user.username;
            document.getElementById('editEmail').value = user.email;
            document.getElementById('editFirstName').value = user.first_name;
            document.getElementById('editLastName').value = user.last_name;
            document.getElementById('editIsActive').checked = user.is_active;

            // Set role based on user permissions
            let role = 'student'; // default
            if (user.is_superuser) {
                role = 'admin';
            } else if (user.is_staff) {
                role = 'teacher';
            }
            document.getElementById('editRole').value = role;

            // Show edit modal
            document.getElementById('editUserModal').style.display = 'flex';
        } else {
            alert('Error loading user data: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error loading user data');
    }
}

function toggleUserStatus(userId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        console.log(`${action} user ${userId}`);
        // Add AJAX call here
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        console.log('Delete user:', userId);
        // Add AJAX call here
    }
}

// Close modal on outside click
document.getElementById('createUserModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCreateUserModal();
    }
});

// Form validation
document.getElementById('createUserForm').addEventListener('submit', function(e) {
    const password = document.querySelector('input[name="password"]').value;
    const confirmPassword = document.querySelector('input[name="password_confirm"]').value;
    
    if (password !== confirmPassword) {
        e.preventDefault();
        alert('Passwords do not match');
        return;
    }
    
    if (password.length < 6) {
        e.preventDefault();
        alert('Password must be at least 6 characters long');
        return;
    }
});
</script>
{% endblock %}
