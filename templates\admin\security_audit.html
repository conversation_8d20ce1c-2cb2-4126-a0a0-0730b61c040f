{% extends 'admin/base_admin.html' %}

{% block title %}Security Audit - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">🔒</span>
            Security Audit Dashboard
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            Comprehensive security assessment and vulnerability analysis
        </p>
    </div>
    
    <div class="admin-header-actions">
        <form method="post" style="display: inline;">
            {% csrf_token %}
            <button type="submit" name="action" value="run_audit" class="btn btn-primary">
                🔍 Run Security Audit
            </button>
        </form>
    </div>
</div>

<div class="admin-content">
    {% if has_results %}
        <!-- Security Score Overview -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📊 Security Score</h3>
            </div>
            
            <div class="security-score-container">
                <div class="security-score {{ audit_results.summary.risk_level|lower }}">
                    <div class="score-circle">
                        <div class="score-value">{{ audit_results.summary.security_score }}</div>
                        <div class="score-label">/ 100</div>
                    </div>
                </div>
                
                <div class="score-details">
                    <div class="score-item">
                        <span class="score-item-label">Risk Level:</span>
                        <span class="score-item-value risk-{{ audit_results.summary.risk_level|lower }}">
                            {{ audit_results.summary.risk_level }}
                        </span>
                    </div>
                    
                    <div class="score-item">
                        <span class="score-item-label">Total Issues:</span>
                        <span class="score-item-value">{{ audit_results.summary.total_issues }}</span>
                    </div>
                    
                    <div class="score-item">
                        <span class="score-item-label">Last Audit:</span>
                        <span class="score-item-value">{{ audit_results.timestamp|date:"M d, Y H:i" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Categories -->
        <div class="admin-grid admin-grid-2">
            {% for category, results in audit_results.items %}
                {% if category != 'summary' and category != 'timestamp' %}
                    <div class="admin-card">
                        <div class="admin-card-header">
                            <h3 style="color: var(--text-primary); margin: 0;">
                                {{ category|title }}
                            </h3>
                            <div class="category-status {{ results.status|lower }}">
                                {% if results.status == 'PASS' %}✅{% else %}❌{% endif %}
                                {{ results.status }}
                            </div>
                        </div>
                        
                        {% if results.issues %}
                            <div class="issues-list">
                                <h4>Issues Found:</h4>
                                {% for issue in results.issues %}
                                    <div class="issue-item severity-{{ issue.severity|lower }}">
                                        <div class="issue-header">
                                            <span class="issue-severity">{{ issue.severity }}</span>
                                            <span class="issue-title">{{ issue.issue }}</span>
                                        </div>
                                        <div class="issue-description">{{ issue.description }}</div>
                                        <div class="issue-fix">
                                            <strong>Fix:</strong> {{ issue.fix }}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        {% if results.recommendations %}
                            <div class="recommendations-list">
                                <h4>Recommendations:</h4>
                                {% for rec in results.recommendations %}
                                    <div class="recommendation-item priority-{{ rec.priority|lower }}">
                                        <div class="rec-header">
                                            <span class="rec-priority">{{ rec.priority }}</span>
                                            <span class="rec-setting">{{ rec.setting }}</span>
                                        </div>
                                        <div class="rec-description">{{ rec.description }}</div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            {% endfor %}
        </div>

        <!-- Priority Recommendations -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">🎯 Priority Recommendations</h3>
            </div>
            
            <div class="priority-recommendations">
                {% for recommendation in audit_results.summary.recommendations %}
                    <div class="priority-item">
                        <div class="priority-number">{{ forloop.counter }}</div>
                        <div class="priority-text">{{ recommendation }}</div>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% else %}
        <!-- No Results State -->
        <div class="admin-card">
            <div class="empty-state">
                <div class="empty-icon">🔍</div>
                <div class="empty-text">No Security Audit Results</div>
                <div class="empty-subtext">Run a security audit to assess your system's security posture</div>
                
                <form method="post" style="margin-top: 2rem;">
                    {% csrf_token %}
                    <button type="submit" name="action" value="run_audit" class="btn btn-primary">
                        🔍 Run First Security Audit
                    </button>
                </form>
            </div>
        </div>
    {% endif %}
</div>

{% endblock %}

{% block extra_css %}
<style>
.security-score-container {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: center;
    padding: 2rem;
}

.security-score {
    display: flex;
    justify-content: center;
    align-items: center;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    border: 4px solid;
}

.security-score.low .score-circle {
    border-color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
}

.security-score.medium .score-circle {
    border-color: #fbbf24;
    background: rgba(251, 191, 36, 0.1);
}

.security-score.high .score-circle,
.security-score.critical .score-circle {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.score-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.score-label {
    font-size: 1rem;
    color: var(--text-muted);
}

.score-details {
    display: grid;
    gap: 1rem;
}

.score-item {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.score-item-label {
    color: var(--text-muted);
    font-weight: 500;
}

.score-item-value {
    color: var(--text-primary);
    font-weight: 600;
}

.risk-low { color: #22c55e; }
.risk-medium { color: #fbbf24; }
.risk-high { color: #f97316; }
.risk-critical { color: #ef4444; }

.category-status {
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.category-status.pass {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.category-status.fail {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.issues-list,
.recommendations-list {
    margin-top: 1rem;
}

.issues-list h4,
.recommendations-list h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1rem;
}

.issue-item {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid;
}

.issue-item.severity-critical {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: #ef4444;
}

.issue-item.severity-high {
    background: rgba(249, 115, 22, 0.1);
    border-left-color: #f97316;
}

.issue-item.severity-medium {
    background: rgba(251, 191, 36, 0.1);
    border-left-color: #fbbf24;
}

.issue-item.severity-low {
    background: rgba(59, 130, 246, 0.1);
    border-left-color: #3b82f6;
}

.issue-header {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 0.5rem;
}

.issue-severity {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.issue-severity {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.issue-title {
    font-weight: 600;
    color: var(--text-primary);
}

.issue-description {
    color: var(--text-muted);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.issue-fix {
    color: var(--text-primary);
    font-size: 0.9rem;
}

.recommendation-item {
    padding: 1rem;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    border-left: 4px solid;
}

.recommendation-item.priority-high {
    border-left-color: #ef4444;
}

.recommendation-item.priority-medium {
    border-left-color: #fbbf24;
}

.recommendation-item.priority-low {
    border-left-color: #3b82f6;
}

.rec-header {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 0.5rem;
}

.rec-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.rec-setting {
    font-weight: 600;
    color: var(--text-primary);
}

.rec-description {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.priority-recommendations {
    display: grid;
    gap: 1rem;
}

.priority-item {
    display: flex;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.priority-number {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: var(--primary);
    color: black;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.priority-text {
    color: var(--text-primary);
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.empty-subtext {
    color: var(--text-muted);
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh audit results every 5 minutes if page is active
let autoRefreshInterval = setInterval(function() {
    if (document.visibilityState === 'visible') {
        // Only refresh if we have results and page has been open for a while
        if ({{ has_results|yesno:'true,false' }}) {
            console.log('Auto-refreshing security audit...');
            // Could implement AJAX refresh here instead of full page reload
        }
    }
}, 300000); // 5 minutes

// Clear interval when page is unloaded
window.addEventListener('beforeunload', function() {
    clearInterval(autoRefreshInterval);
});
</script>
{% endblock %}
