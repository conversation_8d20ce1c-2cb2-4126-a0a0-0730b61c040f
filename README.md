# 🎯 KERNELiOS Advanced Simulator System

A comprehensive web-based cybersecurity simulation platform built with Django 5, featuring advanced security measures, real-time scoring, and professional cyber-themed UI.

## ✨ Features

### 🔐 Security Features
- **CSRF Protection**: All forms protected with Django CSRF tokens
- **Masked Input Fields**: All answer inputs are password-masked for security
- **Sequential Question Access**: Students can only answer questions in order
- **Server-Side Validation**: All answers validated on the server
- **Rate Limiting**: Protection against brute force attacks
- **Session Security**: Secure session management with timeouts

### 📊 Exam Management
- **Multiple Test Versions**: Support for different exam versions (V1, V2, etc.)
- **Real-Time Progress Tracking**: Live monitoring of student progress
- **Complex Scoring System**: Advanced scoring with attempt and time penalties
- **Pause/Resume Functionality**: Teachers can pause exams system-wide
- **Auto-Save Progress**: Student progress automatically saved

### 👥 User Roles
- **Students**: Take exams, view progress, see detailed score breakdowns
- **Teachers**: Monitor progress, reset passwords, control exam flow
- **Administrators**: Full system management, question import/export, configuration

### 📈 Reporting & Analytics
- **Real-Time Scoreboard**: Live rankings with automatic updates
- **Detailed Score Breakdown**: Question-by-question analysis with penalties
- **Excel Export**: Comprehensive results export with multiple sheets
- **Email Reports**: Automated email delivery of results
- **Progress Statistics**: Detailed analytics for teachers and admins

### 🎨 User Interface
- **Cyber-Professional Theme**: Modern dark theme with gold accents
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Real-Time Updates**: Live progress updates and notifications
- **Intuitive Navigation**: Easy-to-use interface for all user types

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation

1. **Clone or download the project**
   ```bash
   cd Simulator-New
   ```

2. **Run the auto-start script**
   
   **Windows:**
   ```bash
   start.bat
   ```
   
   **Linux/Mac:**
   ```bash
   ./start.sh
   ```

3. **Access the system**
   - The system will automatically open in your browser
   - Default URL: `http://localhost:5000`
   - Network access: `http://[your-ip]:5000`

### Default Accounts

| Role | Username | Password |
|------|----------|----------|
| Admin | admin | admin123 |
| Teacher | teacher | teacher123 |

## 📋 System Requirements

### Minimum Requirements
- **OS**: Windows 10, macOS 10.14, or Linux (Ubuntu 18.04+)
- **RAM**: 2GB minimum, 4GB recommended
- **Storage**: 1GB free space
- **Network**: Local network access for multi-user deployment

### Recommended Setup
- **RAM**: 8GB for optimal performance with many concurrent users
- **CPU**: Multi-core processor for better performance
- **Network**: Gigabit Ethernet for large deployments

## 🔧 Configuration

### Exam Configuration
1. **Access Admin Panel**: `/admin/`
2. **Configure Scoring**: Adjust penalties and thresholds
3. **Manage Test Versions**: Create and activate exam versions
4. **Import Questions**: Bulk import from CSV files

### Email Configuration
1. Go to **Admin Panel** → **App Configurations**
2. Set up SMTP settings:
   - Email Host: Your SMTP server
   - Email Port: Usually 587 for TLS
   - Email User/Password: Your email credentials

### Network Configuration
- Default: Accessible on all network interfaces (0.0.0.0:5000)
- Modify in **Admin Panel** → **App Configurations** → **Server Settings**

## 📊 Scoring System

### Base Scoring
- Each question has a configurable point value (default: 5 points)
- Bonus questions available with separate scoring

### Penalty System
- **Attempt Penalties**: 
  - First incorrect attempt: No penalty
  - Each additional incorrect attempt: -0.5 points
  - Maximum penalty per question: -1.5 points

- **Time Penalties**:
  - First 5 minutes: No penalty
  - Each additional minute: -0.25 points
  - Maximum penalty per question: -1.5 points

### Final Score Calculation
```
Final Score = Base Points - Attempt Penalties - Time Penalties
(Minimum score per question: 0 points)
```

## 📁 File Structure

```
Simulator-New/
├── exam/                   # Main Django app
│   ├── models.py          # Database models
│   ├── views.py           # View controllers
│   ├── forms.py           # Form definitions
│   ├── admin.py           # Admin interface
│   ├── scoring.py         # Scoring algorithms
│   ├── middleware.py      # Security middleware
│   └── tests.py           # Test suite
├── templates/             # HTML templates
│   ├── exam/             # Exam-specific templates
│   └── admin/            # Admin templates
├── assets/               # Static assets (logos, etc.)
├── start.py              # Auto-start script
├── start.bat             # Windows startup
├── start.sh              # Linux/Mac startup
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## 🔍 Troubleshooting

### Common Issues

**Port Already in Use**
- The system will automatically try the next available port
- Check the console output for the actual port being used

**Database Errors**
- Run: `python manage.py migrate`
- If issues persist, delete `db.sqlite3` and run migrations again

**Permission Errors (Linux/Mac)**
- Make start script executable: `chmod +x start.sh`

**Email Not Working**
- Verify SMTP settings in Admin Panel
- Check firewall settings for outbound email

### Getting Help
1. Check the console output for error messages
2. Review the `exam_security.log` file for security-related issues
3. Ensure all requirements are installed: `pip install -r requirements.txt`

## 🧪 Testing

Run the test suite to verify system functionality:

```bash
python manage.py test exam
```

### Test Coverage
- Model relationships and data integrity
- Scoring algorithm accuracy
- Authentication and authorization
- Security features and CSRF protection
- Exam logic and sequential answering

## 🚀 Deployment

### Development Deployment
- Use the provided start scripts for local development
- SQLite database included for easy setup

### Production Deployment
1. **Database**: Configure PostgreSQL in settings
2. **Security**: Update security settings for production
3. **Static Files**: Configure proper static file serving
4. **HTTPS**: Enable SSL/TLS certificates
5. **Environment**: Set `DEBUG = False` in production

## 📄 License

This project is proprietary software. All rights reserved.

## 🤝 Support

For technical support or questions about the Secure Exam System, please contact the development team.

---

**Built with ❤️ using Django 5 and modern web technologies**
