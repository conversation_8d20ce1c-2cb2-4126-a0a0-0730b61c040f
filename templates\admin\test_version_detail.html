{% extends 'admin/admin_base.html' %}

{% block title %}{{ version.name }} - Test Version Details{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">{{ version.name|lower }}_questions.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    📚 {{ version.name }}
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > {{ version.description|default:"No description provided" }}
                </p>
            </div>
            <div style="display: flex; gap: 1rem;">
                <button onclick="showQuestionBuilder()" class="btn btn-primary">
                    ➕ Add Question
                </button>
                <a href="{% url 'admin_test_versions' %}" class="btn btn-secondary">
                    🔙 Back to Versions
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Score Distribution -->
<div class="admin-grid admin-grid-4">
    <div class="admin-card">
        <h3>📊 Total Questions</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ questions.count }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Normal: {{ score_distribution.normal_count }} | Bonus: {{ score_distribution.bonus_count }}
        </p>
    </div>
    
    <div class="admin-card">
        <h3>🎯 Score Distribution</h3>
        <div style="font-size: 2rem; color: var(--success); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ score_distribution.normal_total }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Normal points (target: 100)
        </p>
    </div>
    
    <div class="admin-card">
        <h3>🎁 Bonus Points</h3>
        <div style="font-size: 2rem; color: var(--accent); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ score_distribution.bonus_total }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Extra credit available
        </p>
    </div>
    
    <div class="admin-card">
        <h3>⚡ Quick Actions</h3>
        <div style="display: grid; gap: 0.5rem; margin-top: 1rem;">
            <button onclick="recalculateScores()" class="btn btn-secondary">
                🔄 Recalculate Scores
            </button>
            <button onclick="showImportModal()" class="btn btn-secondary">
                📥 Import Questions
            </button>
        </div>
    </div>
</div>

<!-- Questions List -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">question_list.json</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <h3 style="color: var(--primary);">📝 Questions</h3>
            <div style="display: flex; gap: 1rem; align-items: center;">
                <select id="questionTypeFilter" class="form-select" style="width: auto;">
                    <option value="">All Types</option>
                    <option value="text">📝 Text Answer</option>
                    <option value="multiple_choice">🔘 Multiple Choice</option>
                    <option value="true_false">✅ True/False</option>
                </select>
                <select id="bonusFilter" class="form-select" style="width: auto;">
                    <option value="">All Questions</option>
                    <option value="normal">Normal Only</option>
                    <option value="bonus">Bonus Only</option>
                </select>
            </div>
        </div>
        
        {% if questions %}
            <div id="questionsList" style="display: grid; gap: 1rem;">
                {% for question in questions %}
                <div class="question-item" 
                     data-type="{{ question.question_type }}" 
                     data-bonus="{% if question.is_bonus %}bonus{% else %}normal{% endif %}"
                     style="background: var(--dark-card); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 0.75rem; padding: 1.5rem; border-left: 4px solid {% if question.is_bonus %}var(--accent){% else %}var(--primary){% endif %};">
                    
                    <!-- Question Header -->
                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                                <h4 style="margin: 0; color: var(--text-primary); font-family: 'JetBrains Mono', monospace;">
                                    {{ question.order }}. {{ question.name }}
                                </h4>
                                <span style="background: {% if question.question_type == 'text' %}var(--primary){% elif question.question_type == 'multiple_choice' %}var(--success){% else %}var(--warning){% endif %}; color: var(--dark); padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem; font-weight: bold;">
                                    {{ question.get_question_type_display_icon }} {{ question.get_question_type_display }}
                                </span>
                                {% if question.is_bonus %}
                                    <span style="background: var(--accent); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem; font-weight: bold;">
                                        🎁 BONUS
                                    </span>
                                {% endif %}
                            </div>
                            <p style="color: var(--text-secondary); margin: 0; font-size: 0.9rem;">
                                {{ question.description|truncatechars:100 }}
                            </p>
                        </div>
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <span style="color: var(--primary); font-weight: bold; font-family: 'JetBrains Mono', monospace; font-size: 1.1rem;">
                                {{ question.score }} pts
                            </span>
                            <div style="display: flex; gap: 0.5rem;">
                                <button onclick="editQuestion({{ question.id }})" class="btn btn-secondary" style="padding: 0.5rem;">
                                    ✏️
                                </button>
                                <button onclick="deleteQuestion({{ question.id }})" class="btn btn-danger" style="padding: 0.5rem;">
                                    🗑️
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Question Details -->
                    <div style="display: grid; gap: 1rem; font-size: 0.9rem;">
                        <div>
                            <strong style="color: var(--text-secondary);">Question:</strong>
                            <div style="color: var(--text-primary); margin-top: 0.25rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem;">
                                {{ question.description }}
                            </div>
                        </div>
                        
                        {% if question.question_type == 'multiple_choice' and question.choices %}
                        <div>
                            <strong style="color: var(--text-secondary);">Choices:</strong>
                            <div style="margin-top: 0.5rem; display: grid; gap: 0.25rem;">
                                {% for key, value in question.choices.items %}
                                <div style="padding: 0.5rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.25rem; {% if key == question.correct_answer %}border-left: 3px solid var(--success);{% endif %}">
                                    <strong>{{ key }}:</strong> {{ value }}
                                    {% if key == question.correct_answer %}
                                        <span style="color: var(--success); margin-left: 0.5rem;">✓ Correct</span>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% else %}
                        <div>
                            <strong style="color: var(--text-secondary);">Correct Answer:</strong>
                            <code style="background: rgba(0, 255, 65, 0.1); color: var(--success); padding: 0.5rem; border-radius: 0.25rem; margin-left: 0.5rem; font-family: 'JetBrains Mono', monospace;">
                                {{ question.correct_answer }}
                            </code>
                        </div>
                        {% endif %}
                        
                        {% if question.explanation %}
                        <div>
                            <strong style="color: var(--text-secondary);">Explanation:</strong>
                            <div style="color: var(--text-muted); margin-top: 0.25rem; font-style: italic;">
                                {{ question.explanation }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div style="text-align: center; padding: 4rem 2rem; color: var(--text-muted);">
                <div style="font-size: 4rem; margin-bottom: 1rem;">❓</div>
                <h3 style="color: var(--text-secondary); margin-bottom: 1rem;">No Questions Found</h3>
                <p style="margin-bottom: 2rem;">Start building your exam by adding questions.</p>
                <button onclick="showQuestionBuilder()" class="btn btn-primary">
                    ➕ Add First Question
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Question Builder Modal (placeholder) -->
<div id="questionBuilderModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 2000; align-items: center; justify-content: center;">
    <div class="terminal-window" style="width: 90%; max-width: 800px; margin: 0; max-height: 90vh; overflow-y: auto;">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">question_builder.py</div>
            <button onclick="closeQuestionBuilder()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.2rem;">×</button>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">🔧 Question Builder</h3>
            <p style="color: var(--text-secondary);">Question builder interface will be implemented in the next task.</p>
            <button onclick="closeQuestionBuilder()" class="btn btn-secondary">Close</button>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Filter functionality
document.getElementById('questionTypeFilter').addEventListener('change', filterQuestions);
document.getElementById('bonusFilter').addEventListener('change', filterQuestions);

function filterQuestions() {
    const typeFilter = document.getElementById('questionTypeFilter').value;
    const bonusFilter = document.getElementById('bonusFilter').value;
    const questions = document.querySelectorAll('.question-item');
    
    questions.forEach(question => {
        let show = true;
        
        if (typeFilter && question.dataset.type !== typeFilter) {
            show = false;
        }
        
        if (bonusFilter && question.dataset.bonus !== bonusFilter) {
            show = false;
        }
        
        question.style.display = show ? 'block' : 'none';
    });
}

function showQuestionBuilder() {
    document.getElementById('questionBuilderModal').style.display = 'flex';
}

function closeQuestionBuilder() {
    document.getElementById('questionBuilderModal').style.display = 'none';
}

function editQuestion(questionId) {
    console.log('Edit question:', questionId);
    // Implementation will be added with question builder
}

function deleteQuestion(questionId) {
    if (confirm('Are you sure you want to delete this question?')) {
        console.log('Delete question:', questionId);
        // Add AJAX call here
    }
}

function recalculateScores() {
    if (confirm('Recalculate all question scores automatically?')) {
        console.log('Recalculate scores');
        // Add AJAX call here
    }
}

function showImportModal() {
    console.log('Show import modal');
    // Implementation for import modal
}

// Close modal on outside click
document.getElementById('questionBuilderModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuestionBuilder();
    }
});
</script>
{% endblock %}
