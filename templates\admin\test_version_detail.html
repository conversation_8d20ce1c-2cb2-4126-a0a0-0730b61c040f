{% extends 'admin/base_admin.html' %}

{% block title %}{{ version.name }} - Test Version Details{% endblock %}

{% block content %}
{% csrf_token %}
<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">{{ version.name|lower }}_questions.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    📚 {{ version.name }}
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > {{ version.description|default:"No description provided" }}
                </p>
            </div>
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <button onclick="showQuestionBuilder()" class="btn btn-primary">
                    ➕ Add Question
                </button>
                <button onclick="showImportModal()" class="btn btn-success">
                    📥 Import CSV
                </button>
                <a href="{% url 'admin_panel:admin_export_test_version_csv' version.id %}" class="btn btn-terminal">
                    📊 Export CSV
                </a>
                <a href="{% url 'admin_panel:admin_download_csv_template' %}" class="btn btn-secondary" target="_blank">
                    📋 CSV Template
                </a>
                <a href="{% url 'admin_panel:admin_test_versions' %}" class="btn btn-secondary">
                    🔙 Back to Versions
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Score Distribution -->
<div class="admin-grid admin-grid-4">
    <div class="admin-card">
        <h3>📊 Total Questions</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ questions.count }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Normal: {{ score_distribution.normal_count }} | Bonus: {{ score_distribution.bonus_count }}
        </p>
    </div>
    
    <div class="admin-card">
        <h3>🎯 Score Distribution</h3>
        <div style="font-size: 2rem; color: var(--success); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ score_distribution.normal_total }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Normal points (target: 100)
        </p>
    </div>
    
    <div class="admin-card">
        <h3>🎁 Bonus Points</h3>
        <div style="font-size: 2rem; color: var(--accent); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ score_distribution.bonus_total }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Extra credit available
        </p>
    </div>
    
    <div class="admin-card">
        <h3>⚡ Quick Actions</h3>
        <div style="display: grid; gap: 0.5rem; margin-top: 1rem;">
            <a href="{% url 'admin_panel:admin_scoring_manager' version.id %}" class="btn btn-primary">
                🎯 Scoring Manager
            </a>
            <button onclick="recalculateScores()" class="btn btn-secondary">
                🔄 Recalculate Scores
            </button>
            <button onclick="showImportModal()" class="btn btn-secondary">
                📥 Import Questions
            </button>
        </div>
    </div>
</div>

<!-- Questions List -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">question_list.json</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <h3 style="color: var(--primary);">📝 Questions</h3>
            <div style="display: flex; gap: 1rem; align-items: center;">
                <select id="questionTypeFilter" class="form-select" style="width: auto;">
                    <option value="">All Types</option>
                    <option value="text">📝 Text Answer</option>
                    <option value="multiple_choice">🔘 Multiple Choice</option>
                    <option value="true_false">✅ True/False</option>
                </select>
                <select id="bonusFilter" class="form-select" style="width: auto;">
                    <option value="">All Questions</option>
                    <option value="normal">Normal Only</option>
                    <option value="bonus">Bonus Only</option>
                </select>
            </div>
        </div>
        
        {% if questions %}
            <div id="questionsList" style="display: grid; gap: 1rem;">
                {% for question in questions %}
                <div class="question-item" 
                     data-type="{{ question.question_type }}" 
                     data-bonus="{% if question.is_bonus %}bonus{% else %}normal{% endif %}"
                     style="background: var(--dark-card); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 0.75rem; padding: 1.5rem; border-left: 4px solid {% if question.is_bonus %}var(--accent){% else %}var(--primary){% endif %};">
                    
                    <!-- Question Header -->
                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                                <h4 style="margin: 0; color: var(--text-primary); font-family: 'JetBrains Mono', monospace;">
                                    {{ question.order }}. {{ question.name }}
                                </h4>
                                <span style="background: {% if question.question_type == 'text' %}var(--primary){% elif question.question_type == 'multiple_choice' %}var(--success){% else %}var(--warning){% endif %}; color: var(--dark); padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem; font-weight: bold;">
                                    {{ question.get_question_type_display_icon }} {{ question.get_question_type_display }}
                                </span>
                                {% if question.is_bonus %}
                                    <span style="background: var(--accent); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem; font-weight: bold;">
                                        🎁 BONUS
                                    </span>
                                {% endif %}
                            </div>
                            <p style="color: var(--text-secondary); margin: 0; font-size: 0.9rem;">
                                {{ question.description|truncatechars:100 }}
                            </p>
                        </div>
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <span style="color: var(--primary); font-weight: bold; font-family: 'JetBrains Mono', monospace; font-size: 1.1rem;">
                                {{ question.score }} pts
                            </span>
                            <div style="display: flex; gap: 0.5rem;">
                                <button onclick="editQuestion({{ question.id }})" class="btn btn-secondary" style="padding: 0.5rem;">
                                    ✏️
                                </button>
                                <button onclick="deleteQuestion({{ question.id }})" class="btn btn-danger" style="padding: 0.5rem;">
                                    🗑️
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Question Details -->
                    <div style="display: grid; gap: 1rem; font-size: 0.9rem;">
                        <div>
                            <strong style="color: var(--text-secondary);">Question:</strong>
                            <div style="color: var(--text-primary); margin-top: 0.25rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem;">
                                {{ question.description }}
                            </div>
                        </div>
                        
                        {% if question.question_type == 'multiple_choice' and question.choices %}
                        <div>
                            <strong style="color: var(--text-secondary);">Choices:</strong>
                            <div style="margin-top: 0.5rem; display: grid; gap: 0.25rem;">
                                {% for key, value in question.choices.items %}
                                <div style="padding: 0.5rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.25rem; {% if key == question.correct_answer %}border-left: 3px solid var(--success);{% endif %}">
                                    <strong>{{ key }}:</strong> {{ value }}
                                    {% if key == question.correct_answer %}
                                        <span style="color: var(--success); margin-left: 0.5rem;">✓ Correct</span>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% else %}
                        <div>
                            <strong style="color: var(--text-secondary);">Correct Answer:</strong>
                            <code style="background: rgba(0, 255, 65, 0.1); color: var(--success); padding: 0.5rem; border-radius: 0.25rem; margin-left: 0.5rem; font-family: 'JetBrains Mono', monospace;">
                                {{ question.correct_answer }}
                            </code>
                        </div>
                        {% endif %}
                        
                        {% if question.explanation %}
                        <div>
                            <strong style="color: var(--text-secondary);">Explanation:</strong>
                            <div style="color: var(--text-muted); margin-top: 0.25rem; font-style: italic;">
                                {{ question.explanation }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div style="text-align: center; padding: 4rem 2rem; color: var(--text-muted);">
                <div style="font-size: 4rem; margin-bottom: 1rem;">❓</div>
                <h3 style="color: var(--text-secondary); margin-bottom: 1rem;">No Questions Found</h3>
                <p style="margin-bottom: 2rem;">Start building your exam by adding questions.</p>
                <button onclick="showQuestionBuilder()" class="btn btn-primary">
                    ➕ Add First Question
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Question Builder Modal -->
<div id="questionBuilderModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 2000; align-items: center; justify-content: center;">
    <div class="terminal-window" style="width: 95%; max-width: 1000px; margin: 0; max-height: 95vh; overflow-y: auto;">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">question_builder.py</div>
            <button onclick="closeQuestionBuilder()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.2rem;">×</button>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">🔧 Interactive Question Builder</h3>

            <form id="questionBuilderForm" method="post" action="{% url 'admin_panel:admin_create_question' version.id %}">
                {% csrf_token %}

                <!-- Question Type Selection -->
                <div class="form-group">
                    <label class="form-label">📝 Question Type</label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent; transition: all 0.3s ease;" class="question-type-option" data-type="text">
                            <input type="radio" name="question_type" value="text" checked style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">📝 Text Answer</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Single correct text answer</div>
                            </div>
                        </label>

                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent; transition: all 0.3s ease;" class="question-type-option" data-type="multiple_choice">
                            <input type="radio" name="question_type" value="multiple_choice" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">🔘 Multiple Choice</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">4 options with one correct</div>
                            </div>
                        </label>

                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent; transition: all 0.3s ease;" class="question-type-option" data-type="true_false">
                            <input type="radio" name="question_type" value="true_false" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">✅ True/False</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Simple boolean question</div>
                            </div>
                        </label>

                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent; transition: all 0.3s ease;" class="question-type-option" data-type="code_execution">
                            <input type="radio" name="question_type" value="code_execution" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">💻 Code Execution</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Programming with auto-grading</div>
                            </div>
                        </label>

                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 2px solid transparent; transition: all 0.3s ease;" class="question-type-option" data-type="drag_drop">
                            <input type="radio" name="question_type" value="drag_drop" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">🎯 Drag & Drop</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Interactive matching questions</div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Basic Question Info -->
                <div class="admin-grid admin-grid-2">
                    <div class="form-group">
                        <label class="form-label">🏷️ Question Title *</label>
                        <input type="text" name="name" class="form-input" placeholder="e.g., Port Scanning Basics" required>
                        <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                            Short, descriptive title for the question
                        </small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">📊 Question Order</label>
                        <input type="number" name="order" class="form-input" value="{{ questions.count|add:1 }}" min="1" required>
                        <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                            Position in the exam (auto-calculated)
                        </small>
                    </div>
                </div>

                <!-- Question Description -->
                <div class="form-group">
                    <label class="form-label">❓ Question Text *</label>
                    <textarea name="description" class="form-textarea" rows="4" placeholder="Enter the full question text here..." required></textarea>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        The actual question that students will see
                    </small>
                </div>

                <!-- Answer Section (Dynamic based on question type) -->
                <div id="answerSection">
                    <!-- Text Answer Section -->
                    <div id="textAnswerSection" class="answer-section">
                        <div class="form-group">
                            <label class="form-label">✅ Correct Answer *</label>
                            <input type="text" name="correct_answer_text" class="form-input" placeholder="Enter the correct answer">
                            <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                                Case-insensitive matching. Students can enter variations.
                            </small>
                        </div>
                    </div>

                    <!-- Multiple Choice Section -->
                    <div id="multipleChoiceSection" class="answer-section" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">🔘 Answer Choices</label>
                            <div style="display: grid; gap: 1rem;">
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <input type="radio" name="correct_choice" value="A" style="margin: 0;">
                                    <strong style="color: var(--primary); min-width: 20px;">A:</strong>
                                    <input type="text" name="choice_a" class="form-input" placeholder="Option A" style="flex: 1;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <input type="radio" name="correct_choice" value="B" style="margin: 0;">
                                    <strong style="color: var(--primary); min-width: 20px;">B:</strong>
                                    <input type="text" name="choice_b" class="form-input" placeholder="Option B" style="flex: 1;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <input type="radio" name="correct_choice" value="C" style="margin: 0;">
                                    <strong style="color: var(--primary); min-width: 20px;">C:</strong>
                                    <input type="text" name="choice_c" class="form-input" placeholder="Option C" style="flex: 1;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <input type="radio" name="correct_choice" value="D" style="margin: 0;">
                                    <strong style="color: var(--primary); min-width: 20px;">D:</strong>
                                    <input type="text" name="choice_d" class="form-input" placeholder="Option D" style="flex: 1;">
                                </div>
                            </div>
                            <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                                Select the correct answer by clicking the radio button
                            </small>
                        </div>
                    </div>

                    <!-- True/False Section -->
                    <div id="trueFalseSection" class="answer-section" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">✅ Correct Answer</label>
                            <div style="display: flex; gap: 2rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="radio" name="correct_tf" value="A" style="margin: 0;">
                                    <span style="color: var(--success);">True</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                    <input type="radio" name="correct_tf" value="B" style="margin: 0;">
                                    <span style="color: var(--error);">False</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Code Execution Section -->
                    <div id="codeExecutionSection" class="answer-section" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">💻 Programming Language</label>
                            <select name="programming_language" class="form-input">
                                <option value="python">🐍 Python</option>
                                <option value="javascript">🟨 JavaScript</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">📝 Starter Code</label>
                            <textarea name="starter_code" class="form-textarea" rows="8" placeholder="# Write starter code template here...
def solve_problem():
    # Your code here
    pass

# Test your solution
result = solve_problem()
print(result)"></textarea>
                            <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                                Initial code template that students will see
                            </small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">🧪 Test Cases (JSON)</label>
                            <textarea name="test_cases" class="form-textarea" rows="6" placeholder='[
  {
    "input": "",
    "expected": "Hello, World!",
    "description": "Basic output test"
  },
  {
    "input": "5",
    "expected": "10",
    "description": "Input processing test"
  }
]'></textarea>
                            <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                                JSON array of test cases for auto-grading
                            </small>
                        </div>
                    </div>

                    <!-- Drag and Drop Section -->
                    <div id="dragDropSection" class="answer-section" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">🎯 Drag Items (JSON)</label>
                            <textarea name="drag_items" class="form-textarea" rows="4" placeholder='[
  {"id": "item1", "text": "TCP", "category": "protocol"},
  {"id": "item2", "text": "UDP", "category": "protocol"},
  {"id": "item3", "text": "HTTP", "category": "application"}
]'></textarea>
                            <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                                Items that students can drag
                            </small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">📍 Drop Zones (JSON)</label>
                            <textarea name="drop_zones" class="form-textarea" rows="4" placeholder='[
  {"id": "zone1", "text": "Transport Layer", "accepts": ["protocol"]},
  {"id": "zone2", "text": "Application Layer", "accepts": ["application"]}
]'></textarea>
                            <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                                Drop zones where items can be placed
                            </small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">✅ Correct Matches (JSON)</label>
                            <textarea name="correct_matches" class="form-textarea" rows="4" placeholder='[
  {"item": "item1", "zone": "zone1"},
  {"item": "item2", "zone": "zone1"},
  {"item": "item3", "zone": "zone2"}
]'></textarea>
                            <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                                Correct item-to-zone mappings
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Additional Options -->
                <div class="admin-grid admin-grid-2">
                    <div class="form-group">
                        <label class="form-label">🎁 Question Type</label>
                        <div style="display: flex; gap: 2rem;">
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="radio" name="is_bonus" value="false" checked style="margin: 0;">
                                <span style="color: var(--text-primary);">📝 Normal Question</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="radio" name="is_bonus" value="true" style="margin: 0;">
                                <span style="color: var(--accent);">🎁 Bonus Question</span>
                            </label>
                        </div>
                        <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                            Bonus questions provide extra credit
                        </small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">🎯 Points</label>
                        <input type="number" name="score" class="form-input" value="5" min="1" max="50">
                        <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                            Will be auto-calculated to total 100 points
                        </small>
                    </div>
                </div>

                <!-- Optional Explanation -->
                <div class="form-group">
                    <label class="form-label">💡 Explanation (Optional)</label>
                    <textarea name="explanation" class="form-textarea" rows="3" placeholder="Optional explanation shown after answering..."></textarea>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Helps students learn from their answers
                    </small>
                </div>

                <!-- Action Buttons -->
                <div style="display: flex; gap: 1rem; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        ✨ Create Question
                    </button>
                    <button type="button" onclick="previewQuestion()" class="btn btn-secondary">
                        👁️ Preview
                    </button>
                    <button type="button" onclick="closeQuestionBuilder()" class="btn btn-secondary">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- CSV Import Modal -->
<div id="importModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 2000; align-items: center; justify-content: center;">
    <div class="terminal-window" style="width: 90%; max-width: 600px; margin: 0;">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">csv_import.py</div>
            <button onclick="closeImportModal()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.2rem;">×</button>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">📥 Import Questions from CSV</h3>

            <div style="background: rgba(255, 215, 0, 0.1); border: 1px solid var(--primary); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                <h4 style="color: var(--primary); margin-bottom: 0.5rem;">📋 CSV Format Requirements:</h4>
                <ul style="color: var(--text-secondary); margin: 0; padding-left: 1.5rem;">
                    <li><strong>Required columns:</strong> Title, Description, Answer</li>
                    <li><strong>Question types:</strong> text, multiple_choice, true_false</li>
                    <li><strong>Multiple choice format:</strong> A:Option A|B:Option B|C:Option C|D:Option D</li>
                    <li><strong>Boolean fields:</strong> is_bonus (true/false)</li>
                    <li><strong>Download template:</strong> <a href="{% url 'admin_panel:admin_download_csv_template' %}" target="_blank" style="color: var(--primary);">CSV Template</a></li>
                </ul>
            </div>

            <form id="importForm" method="post" action="{% url 'admin_panel:admin_import_test_version_csv' version.id %}" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="form-group">
                    <label class="form-label">📁 Select CSV File *</label>
                    <input type="file" name="csv_file" accept=".csv" class="form-input" required style="padding: 0.75rem;">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Only .csv files are accepted. Maximum file size: 5MB
                    </small>
                </div>

                <div style="background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; padding: 1rem; margin: 1rem 0;">
                    <h4 style="color: var(--text-secondary); margin-bottom: 0.5rem;">⚠️ Import Notes:</h4>
                    <ul style="color: var(--text-muted); margin: 0; padding-left: 1.5rem; font-size: 0.9rem;">
                        <li>Questions will be added to the existing version</li>
                        <li>Scores will be automatically recalculated</li>
                        <li>Invalid rows will be skipped with error messages</li>
                        <li>Order numbers will be adjusted if conflicts exist</li>
                    </ul>
                </div>

                <div style="display: flex; gap: 1rem; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        📥 Import Questions
                    </button>
                    <button type="button" onclick="closeImportModal()" class="btn btn-secondary">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Filter functionality
document.getElementById('questionTypeFilter').addEventListener('change', filterQuestions);
document.getElementById('bonusFilter').addEventListener('change', filterQuestions);

function filterQuestions() {
    const typeFilter = document.getElementById('questionTypeFilter').value;
    const bonusFilter = document.getElementById('bonusFilter').value;
    const questions = document.querySelectorAll('.question-item');

    questions.forEach(question => {
        let show = true;

        if (typeFilter && question.dataset.type !== typeFilter) {
            show = false;
        }

        if (bonusFilter && question.dataset.bonus !== bonusFilter) {
            show = false;
        }

        question.style.display = show ? 'block' : 'none';
    });
}

function showQuestionBuilder() {
    // Reset form
    document.getElementById('questionBuilderForm').reset();

    // Show text answer section by default
    showAnswerSection('text');

    // Update question type styling
    updateQuestionTypeSelection('text');

    document.getElementById('questionBuilderModal').style.display = 'flex';
}

function closeQuestionBuilder() {
    document.getElementById('questionBuilderModal').style.display = 'none';
}

// Question type selection handling
document.querySelectorAll('input[name="question_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        showAnswerSection(this.value);
        updateQuestionTypeSelection(this.value);
    });
});

function showAnswerSection(type) {
    // Hide all answer sections
    document.querySelectorAll('.answer-section').forEach(section => {
        section.style.display = 'none';
    });

    // Show the selected section
    const sectionMap = {
        'text': 'textAnswerSection',
        'multiple_choice': 'multipleChoiceSection',
        'true_false': 'trueFalseSection',
        'code_execution': 'codeExecutionSection',
        'drag_drop': 'dragDropSection'
    };

    const sectionId = sectionMap[type];
    if (sectionId) {
        document.getElementById(sectionId).style.display = 'block';
    }
}

function updateQuestionTypeSelection(type) {
    // Reset all option styles
    document.querySelectorAll('.question-type-option').forEach(option => {
        option.style.borderColor = 'transparent';
        option.style.background = 'rgba(255, 255, 255, 0.05)';
    });

    // Highlight selected option
    const selectedOption = document.querySelector(`.question-type-option[data-type="${type}"]`);
    if (selectedOption) {
        selectedOption.style.borderColor = 'var(--primary)';
        selectedOption.style.background = 'rgba(255, 215, 0, 0.1)';
    }
}

function previewQuestion() {
    const formData = new FormData(document.getElementById('questionBuilderForm'));
    const questionType = formData.get('question_type');
    const title = formData.get('name');
    const description = formData.get('description');

    if (!title || !description) {
        alert('Please fill in the question title and description first');
        return;
    }

    let previewHtml = `
        <div style="background: var(--dark-card); padding: 1.5rem; border-radius: 0.75rem; border-left: 4px solid var(--primary);">
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">${title}</h4>
            <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">${description}</p>
    `;

    if (questionType === 'multiple_choice') {
        const choices = {
            'A': formData.get('choice_a'),
            'B': formData.get('choice_b'),
            'C': formData.get('choice_c'),
            'D': formData.get('choice_d')
        };

        previewHtml += '<div style="display: grid; gap: 0.5rem;">';
        Object.entries(choices).forEach(([key, value]) => {
            if (value) {
                previewHtml += `<div style="padding: 0.5rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.25rem;"><strong>${key}:</strong> ${value}</div>`;
            }
        });
        previewHtml += '</div>';
    } else if (questionType === 'true_false') {
        previewHtml += '<div style="display: flex; gap: 2rem;"><span>A: True</span><span>B: False</span></div>';
    } else if (questionType === 'code_execution') {
        const language = formData.get('programming_language') || 'python';
        const starterCode = formData.get('starter_code') || '';
        previewHtml += `
            <div style="margin-bottom: 1rem;">
                <strong>Language:</strong> ${language.charAt(0).toUpperCase() + language.slice(1)}
            </div>
            <div style="background: #1a1a1a; padding: 1rem; border-radius: 0.5rem; font-family: 'Courier New', monospace;">
                <pre style="margin: 0; color: #f8f8f2;">${starterCode || '# Code editor will appear here'}</pre>
            </div>
        `;
    } else if (questionType === 'drag_drop') {
        previewHtml += `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                <div>
                    <strong>Drag Items:</strong>
                    <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; margin-top: 0.5rem;">
                        <div style="padding: 0.5rem 1rem; background: var(--primary); color: black; border-radius: 1rem; cursor: grab;">Sample Item 1</div>
                        <div style="padding: 0.5rem 1rem; background: var(--primary); color: black; border-radius: 1rem; cursor: grab;">Sample Item 2</div>
                    </div>
                </div>
                <div>
                    <strong>Drop Zones:</strong>
                    <div style="margin-top: 0.5rem;">
                        <div style="padding: 1rem; border: 2px dashed var(--text-muted); border-radius: 0.5rem; margin-bottom: 0.5rem;">Drop Zone 1</div>
                        <div style="padding: 1rem; border: 2px dashed var(--text-muted); border-radius: 0.5rem;">Drop Zone 2</div>
                    </div>
                </div>
            </div>
        `;
    }

    previewHtml += '</div>';

    // Show preview in a simple alert (could be enhanced with a modal)
    const previewWindow = window.open('', '_blank', 'width=600,height=400');
    previewWindow.document.write(`
        <html>
            <head><title>Question Preview</title></head>
            <body style="font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white;">
                <h2>Question Preview</h2>
                ${previewHtml}
                <button onclick="window.close()" style="margin-top: 20px; padding: 10px 20px;">Close</button>
            </body>
        </html>
    `);
}

function editQuestion(questionId) {
    console.log('Edit question:', questionId);
    // Implementation will be added with question builder
}

function deleteQuestion(questionId) {
    if (confirm('Are you sure you want to delete this question?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{% url 'admin_panel:admin_delete_question' version.id 0 %}`.replace('0', questionId);

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function recalculateScores() {
    if (confirm('Recalculate all question scores automatically?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "admin_panel:admin_recalculate_scores" version.id %}';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function showImportModal() {
    document.getElementById('importForm').reset();
    document.getElementById('importModal').style.display = 'flex';
}

function closeImportModal() {
    document.getElementById('importModal').style.display = 'none';
}

// Close modal on outside click
document.getElementById('questionBuilderModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuestionBuilder();
    }
});

document.getElementById('importModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImportModal();
    }
});

// Initialize question type selection on page load
document.addEventListener('DOMContentLoaded', function() {
    updateQuestionTypeSelection('text');
});
</script>
{% endblock %}
