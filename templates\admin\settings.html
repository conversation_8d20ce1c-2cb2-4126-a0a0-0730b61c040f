{% extends 'admin/admin_base.html' %}

{% block title %}System Settings - KERNELiOS Admin{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">system_settings.py</div>
    </div>
    <div class="terminal-content">
        <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
            ⚙️ System Settings
        </h1>
        <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
            > Configure system-wide settings and preferences
        </p>
    </div>
</div>

<form method="post">
    {% csrf_token %}
    
    <!-- Email Configuration -->
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">email_config.json</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">📧 Email Configuration</h3>
            
            <div class="admin-grid admin-grid-2">
                <div class="form-group">
                    <label class="form-label">SMTP Host</label>
                    <input type="text" name="email_host" class="form-input" value="{{ app_config.email_host }}" placeholder="smtp.gmail.com">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        SMTP server hostname
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">SMTP Port</label>
                    <input type="number" name="email_port" class="form-input" value="{{ app_config.email_port }}" placeholder="587">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Usually 587 for TLS or 465 for SSL
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Username</label>
                    <input type="text" name="email_host_user" class="form-input" value="{{ app_config.email_host_user }}" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" name="email_host_password" class="form-input" value="{{ app_config.email_host_password }}" placeholder="App password or email password">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Use app-specific password for Gmail
                    </small>
                </div>
            </div>
            
            <div class="form-group">
                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                    <input type="checkbox" name="email_use_tls" {% if app_config.email_use_tls %}checked{% endif %}>
                    <span style="color: var(--text-primary);">Use TLS Encryption</span>
                </label>
                <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                    Recommended for secure email transmission
                </small>
            </div>
            
            <div style="margin-top: 1.5rem; padding: 1rem; background: rgba(255, 215, 0, 0.1); border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem;">
                <div style="color: var(--primary); font-weight: 500; margin-bottom: 0.5rem;">📧 Email Status</div>
                <div style="color: var(--text-secondary); font-size: 0.9rem;">
                    {% if app_config.email_host %}
                        <span style="color: var(--success);">✅ Configured</span> - Email notifications enabled
                    {% else %}
                        <span style="color: var(--warning);">⚠️ Not Configured</span> - Email notifications disabled
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scoring Configuration -->
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">scoring_config.json</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">🎯 Scoring Configuration</h3>
            
            <div class="admin-grid admin-grid-2">
                <div class="form-group">
                    <label class="form-label">Penalty Start (Attempts)</label>
                    <input type="number" name="incorrect_attempts_penalized_from" class="form-input" 
                           value="{{ scoring_config.incorrect_attempts_penalized_from }}" min="1" max="10">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Start penalizing after this many incorrect attempts
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Penalty per Mistake</label>
                    <input type="number" name="attempt_penalty_per_mistake" class="form-input" 
                           value="{{ scoring_config.attempt_penalty_per_mistake }}" min="0" max="5" step="0.1">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Points deducted per incorrect attempt
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Max Attempt Penalty</label>
                    <input type="number" name="max_attempt_penalty_per_question" class="form-input" 
                           value="{{ scoring_config.max_attempt_penalty_per_question }}" min="0" max="10" step="0.1">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Maximum penalty for attempts per question
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Time Penalty Threshold (Minutes)</label>
                    <input type="number" name="time_penalty_threshold_minutes" class="form-input" 
                           value="{{ scoring_config.time_penalty_threshold_minutes }}" min="1" max="60">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Start time penalty after this many minutes
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Time Penalty per Minute</label>
                    <input type="number" name="time_penalty_per_minute" class="form-input" 
                           value="{{ scoring_config.time_penalty_per_minute }}" min="0" max="2" step="0.05">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Points deducted per minute over threshold
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Max Time Penalty</label>
                    <input type="number" name="max_time_penalty_per_question" class="form-input" 
                           value="{{ scoring_config.max_time_penalty_per_question }}" min="0" max="10" step="0.1">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Maximum time penalty per question
                    </small>
                </div>
            </div>
            
            <div style="margin-top: 1.5rem; padding: 1rem; background: rgba(0, 255, 65, 0.1); border: 1px solid rgba(0, 255, 65, 0.3); border-radius: 0.5rem;">
                <div style="color: var(--success); font-weight: 500; margin-bottom: 0.5rem;">💡 Scoring Formula</div>
                <div style="color: var(--text-secondary); font-size: 0.9rem; font-family: 'JetBrains Mono', monospace;">
                    Final Score = Base Score - min(Attempt Penalty, Max Attempt Penalty) - min(Time Penalty, Max Time Penalty)
                </div>
            </div>
        </div>
    </div>
    
    <!-- Security Settings -->
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">security_config.json</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">🔒 Security Settings</h3>
            
            <div class="admin-grid admin-grid-2">
                <div class="form-group">
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" name="require_email_verification" {% if app_config.require_email_verification %}checked{% endif %}>
                        <span style="color: var(--text-primary);">Require Email Verification</span>
                    </label>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Users must verify email before accessing exams
                    </small>
                </div>
                
                <div class="form-group">
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" name="enable_session_timeout" checked>
                        <span style="color: var(--text-primary);">Enable Session Timeout</span>
                    </label>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Automatically log out inactive users
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Session Timeout (Minutes)</label>
                    <input type="number" name="session_timeout_minutes" class="form-input" value="30" min="5" max="480">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Minutes of inactivity before logout
                    </small>
                </div>
                
                <div class="form-group">
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" name="enable_audit_log" checked>
                        <span style="color: var(--text-primary);">Enable Audit Logging</span>
                    </label>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Log all admin and user actions
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Maintenance -->
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">maintenance.sh</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">🔧 System Maintenance</h3>
            
            <div class="admin-grid admin-grid-2">
                <div class="admin-card">
                    <h4 style="color: var(--text-primary); margin-bottom: 1rem;">🗄️ Database</h4>
                    <div style="display: grid; gap: 0.75rem;">
                        <button type="button" onclick="optimizeDatabase()" class="btn btn-secondary">
                            🔄 Optimize Database
                        </button>
                        <button type="button" onclick="cleanupSessions()" class="btn btn-secondary">
                            🧹 Cleanup Sessions
                        </button>
                        <button type="button" onclick="backupDatabase()" class="btn btn-secondary">
                            💾 Backup Database
                        </button>
                    </div>
                </div>
                
                <div class="admin-card">
                    <h4 style="color: var(--text-primary); margin-bottom: 1rem;">📊 Analytics</h4>
                    <div style="display: grid; gap: 0.75rem;">
                        <button type="button" onclick="generateReport()" class="btn btn-secondary">
                            📈 Generate Report
                        </button>
                        <button type="button" onclick="exportLogs()" class="btn btn-secondary">
                            📋 Export Logs
                        </button>
                        <button type="button" onclick="clearOldData()" class="btn btn-secondary">
                            🗑️ Clear Old Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Save Settings -->
    <div style="display: flex; gap: 1rem; margin-top: 2rem;">
        <button type="submit" class="btn btn-primary" style="flex: 1;">
            💾 Save All Settings
        </button>
        <button type="button" onclick="resetToDefaults()" class="btn btn-secondary">
            🔄 Reset to Defaults
        </button>
        <button type="button" onclick="testEmailConfig()" class="btn btn-secondary">
            📧 Test Email
        </button>
    </div>
</form>

{% endblock %}

{% block extra_js %}
<script>
function optimizeDatabase() {
    if (confirm('Optimize database? This may take a few minutes.')) {
        console.log('Optimizing database...');
        // Add AJAX call here
    }
}

function cleanupSessions() {
    if (confirm('Clean up expired sessions?')) {
        console.log('Cleaning up sessions...');
        // Add AJAX call here
    }
}

function backupDatabase() {
    if (confirm('Create database backup?')) {
        console.log('Creating backup...');
        // Add AJAX call here
    }
}

function generateReport() {
    console.log('Generating system report...');
    // Add AJAX call here
}

function exportLogs() {
    console.log('Exporting logs...');
    // Add AJAX call here
}

function clearOldData() {
    if (confirm('Clear old data? This action cannot be undone.')) {
        console.log('Clearing old data...');
        // Add AJAX call here
    }
}

function resetToDefaults() {
    if (confirm('Reset all settings to defaults? This will overwrite current configuration.')) {
        console.log('Resetting to defaults...');
        // Add AJAX call here
    }
}

function testEmailConfig() {
    const button = event.target;
    const originalText = button.innerHTML;

    button.innerHTML = '⏳ Testing...';
    button.disabled = true;

    fetch('{% url "admin_panel:admin_test_email" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            email_host: document.querySelector('input[name="email_host"]').value,
            email_port: document.querySelector('input[name="email_port"]').value,
            email_use_tls: document.querySelector('input[name="email_use_tls"]').checked,
            email_host_user: document.querySelector('input[name="email_host_user"]').value,
            email_host_password: document.querySelector('input[name="email_host_password"]').value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('✅ Email test successful!', 'success');
        } else {
            showMessage('❌ Email test failed: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showMessage('❌ Email test failed: ' + error.message, 'error');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function showMessage(message, type) {
    // Create a temporary message element
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        color: white;
        font-weight: bold;
        z-index: 9999;
        background: ${type === 'success' ? 'var(--success)' : 'var(--error)'};
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // Remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const emailHost = document.querySelector('input[name="email_host"]').value;
    const emailPort = document.querySelector('input[name="email_port"]').value;
    
    if (emailHost && (!emailPort || emailPort < 1 || emailPort > 65535)) {
        e.preventDefault();
        alert('Please enter a valid email port (1-65535)');
        return;
    }
});
</script>
{% endblock %}
