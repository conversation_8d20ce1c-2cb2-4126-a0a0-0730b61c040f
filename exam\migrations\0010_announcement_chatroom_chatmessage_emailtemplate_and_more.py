# Generated by Django 5.0.14 on 2025-07-27 10:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("exam", "0009_level_correct_matches_level_drag_items_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Announcement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("message", models.TextField()),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("normal", "Normal"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="normal",
                        max_length=10,
                    ),
                ),
                (
                    "audience",
                    models.CharField(
                        choices=[
                            ("all", "All Users"),
                            ("students", "Students Only"),
                            ("teachers", "Teachers Only"),
                            ("instance", "Specific Instance"),
                        ],
                        default="all",
                        max_length=20,
                    ),
                ),
                ("is_active", models.<PERSON>oleanField(default=True)),
                (
                    "show_popup",
                    models.BooleanField(
                        default=False, help_text="Show as popup notification"
                    ),
                ),
                (
                    "auto_dismiss",
                    models.BooleanField(
                        default=False, help_text="Auto-dismiss after reading"
                    ),
                ),
                (
                    "dismiss_after_seconds",
                    models.IntegerField(
                        default=10, help_text="Auto-dismiss time in seconds"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "target_instance",
                    models.ForeignKey(
                        blank=True,
                        help_text="Required if audience is 'instance'",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="exam.examinstance",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ChatRoom",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "room_type",
                    models.CharField(
                        choices=[
                            ("global", "Global Chat"),
                            ("instance", "Instance Chat"),
                            ("support", "Support Chat"),
                            ("private", "Private Chat"),
                        ],
                        default="instance",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("allow_students", models.BooleanField(default=True)),
                ("allow_teachers", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "instance",
                    models.ForeignKey(
                        blank=True,
                        help_text="Required for instance chat rooms",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="exam.examinstance",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("text", "Text Message"),
                            ("system", "System Message"),
                            ("file", "File Attachment"),
                            ("image", "Image"),
                        ],
                        default="text",
                        max_length=10,
                    ),
                ),
                ("content", models.TextField()),
                (
                    "file_attachment",
                    models.FileField(blank=True, null=True, upload_to="chat_files/"),
                ),
                ("file_name", models.CharField(blank=True, max_length=255)),
                ("file_size", models.IntegerField(blank=True, null=True)),
                ("is_edited", models.BooleanField(default=False)),
                ("edited_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "room",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="exam.chatroom",
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="EmailTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("welcome", "Welcome Email"),
                            ("exam_start", "Exam Started"),
                            ("exam_complete", "Exam Completed"),
                            ("results", "Results Available"),
                            ("reminder", "Exam Reminder"),
                            ("password_reset", "Password Reset"),
                            ("instance_created", "Instance Created"),
                            ("registration_open", "Registration Open"),
                            ("custom", "Custom Template"),
                        ],
                        max_length=20,
                    ),
                ),
                ("subject", models.CharField(max_length=200)),
                (
                    "html_content",
                    models.TextField(
                        help_text="HTML email content with template variables"
                    ),
                ),
                (
                    "text_content",
                    models.TextField(blank=True, help_text="Plain text fallback"),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["template_type", "name"],
            },
        ),
        migrations.CreateModel(
            name="NotificationPreference",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email_announcements", models.BooleanField(default=True)),
                ("email_exam_updates", models.BooleanField(default=True)),
                ("email_results", models.BooleanField(default=True)),
                ("email_chat_mentions", models.BooleanField(default=True)),
                ("show_popup_announcements", models.BooleanField(default=True)),
                ("show_chat_notifications", models.BooleanField(default=True)),
                ("show_exam_notifications", models.BooleanField(default=True)),
                ("sms_urgent_only", models.BooleanField(default=True)),
                ("phone_number", models.CharField(blank=True, max_length=20)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="AnnouncementRead",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("read_at", models.DateTimeField(auto_now_add=True)),
                (
                    "announcement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="exam.announcement",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("announcement", "user")},
            },
        ),
    ]
