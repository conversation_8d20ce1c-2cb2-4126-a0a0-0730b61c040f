{% extends 'admin/base_admin.html' %}

{% block title %}Backup Management - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">💾</span>
            Backup Management
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            Create, manage, and restore system backups
        </p>
    </div>
    
    <div class="admin-header-actions">
        <button onclick="createBackup()" class="btn btn-primary">
            📦 Create Backup
        </button>
    </div>
</div>

<div class="admin-content">
    <!-- Backup Statistics -->
    <div class="admin-grid admin-grid-3">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📊 Statistics</h3>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">Total Backups:</span>
                <span class="stat-value">{{ backup_count }}</span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">Total Size:</span>
                <span class="stat-value">{{ total_size }}</span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">Backup Directory:</span>
                <span class="stat-value">{{ backup_dir }}</span>
            </div>
        </div>
        
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">⚙️ Quick Actions</h3>
            </div>
            
            <form method="post" class="backup-actions">
                {% csrf_token %}
                <button type="submit" name="action" value="cleanup_old" class="btn btn-secondary btn-sm">
                    🧹 Cleanup Old Backups
                </button>
                
                <div class="form-group">
                    <label for="keep_days">Keep Days:</label>
                    <input type="number" name="keep_days" value="30" min="1" class="form-input">
                </div>
                
                <div class="form-group">
                    <label for="keep_count">Keep Count:</label>
                    <input type="number" name="keep_count" value="10" min="1" class="form-input">
                </div>
            </form>
        </div>
        
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📋 Backup Options</h3>
            </div>
            
            <form method="post" id="backupForm">
                {% csrf_token %}
                <input type="hidden" name="action" value="create_backup">
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="include_media" checked>
                        Include media files
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="compress" checked>
                        Compress backup
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="encrypt" checked>
                        Encrypt backup
                    </label>
                </div>
            </form>
        </div>
    </div>

    <!-- Backup List -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0;">📁 Available Backups</h3>
        </div>
        
        {% if backups %}
            <div class="backup-list">
                {% for backup in backups %}
                    <div class="backup-item">
                        <div class="backup-info">
                            <div class="backup-name">{{ backup.name }}</div>
                            <div class="backup-details">
                                <span class="backup-size">{{ backup.size }}</span>
                                <span class="backup-date">{{ backup.created_at|date:"M d, Y H:i" }}</span>
                                {% if backup.is_encrypted %}
                                    <span class="backup-badge encrypted">🔒 Encrypted</span>
                                {% endif %}
                                {% if backup.is_compressed %}
                                    <span class="backup-badge compressed">📦 Compressed</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="backup-actions">
                            <form method="post" style="display: inline;">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="delete_backup">
                                <input type="hidden" name="backup_name" value="{{ backup.name }}">
                                <button type="submit" class="btn btn-danger btn-sm" 
                                        onclick="return confirm('Are you sure you want to delete this backup?')">
                                    🗑️ Delete
                                </button>
                            </form>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">📦</div>
                <div class="empty-text">No backups found</div>
                <div class="empty-subtext">Create your first backup to get started</div>
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.backup-actions {
    display: grid;
    gap: 1rem;
}

.backup-list {
    display: grid;
    gap: 1rem;
}

.backup-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.backup-info {
    flex: 1;
}

.backup-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.backup-details {
    display: flex;
    gap: 1rem;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.backup-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.backup-badge.encrypted {
    background: rgba(255, 215, 0, 0.2);
    color: var(--primary);
}

.backup-badge.compressed {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-muted);
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    cursor: pointer;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-text {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function createBackup() {
    document.getElementById('backupForm').submit();
}
</script>
{% endblock %}
