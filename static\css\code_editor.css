/* Code Editor Styles */
.code-editor-container {
    background: var(--dark-card);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 215, 0, 0.2);
    overflow: hidden;
    font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
}

.code-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    padding: 0.5rem 1rem;
}

.code-editor-tabs {
    display: flex;
    gap: 0.5rem;
}

.code-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.code-tab:hover {
    background: rgba(255, 215, 0, 0.1);
    color: var(--text-primary);
}

.code-tab.active {
    background: var(--primary);
    color: black;
    font-weight: 600;
}

.code-editor-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-code-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary);
    color: black;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-code-action:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-code-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.code-editor-content {
    position: relative;
    min-height: 400px;
}

.code-tab-content {
    display: none;
    height: 400px;
}

.code-tab-content.active {
    display: block;
}

.code-editor-wrapper {
    display: flex;
    height: 100%;
    background: #1a1a1a;
}

.line-numbers {
    background: #2a2a2a;
    color: #666;
    padding: 1rem 0.5rem;
    text-align: right;
    user-select: none;
    min-width: 50px;
    font-size: 0.9rem;
    line-height: 1.5;
    overflow: hidden;
}

.line-number {
    height: 1.5em;
    padding-right: 0.5rem;
}

.code-textarea {
    flex: 1;
    background: #1a1a1a;
    color: #f8f8f2;
    border: none;
    outline: none;
    padding: 1rem;
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: none;
    tab-size: 4;
}

.code-textarea::placeholder {
    color: #666;
}

/* Python syntax highlighting (basic) */
.python-syntax {
    /* This would be enhanced with a proper syntax highlighter */
}

.code-editor-output {
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    max-height: 200px;
    overflow-y: auto;
}

.output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 215, 0, 0.1);
}

.output-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.btn-clear-output {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.btn-clear-output:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.output-content {
    padding: 1rem;
    min-height: 100px;
}

.output-placeholder {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

.execution-result {
    color: var(--text-primary);
}

.execution-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
}

.execution-status.success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.execution-status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.execution-time {
    margin-left: auto;
    font-size: 0.8rem;
    opacity: 0.7;
}

.output-section, .error-section {
    margin-bottom: 1rem;
}

.output-section h4, .error-section h4 {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.output-text, .error-text {
    background: rgba(0, 0, 0, 0.3);
    padding: 0.75rem;
    border-radius: 0.5rem;
    border-left: 3px solid var(--primary);
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.error-text {
    border-left-color: #ef4444;
    color: #fca5a5;
}

.execution-error {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    padding: 1rem;
    border-radius: 0.5rem;
}

/* Test Results Styles */
.test-results-container {
    padding: 1rem;
    height: 100%;
    overflow-y: auto;
}

.test-placeholder {
    text-align: center;
    color: var(--text-muted);
    padding: 4rem 2rem;
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.test-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.test-results-header h3 {
    color: var(--text-primary);
    margin: 0;
}

.test-score {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.score-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.score-value {
    font-weight: 700;
    font-size: 1.1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
}

.score-value.passing {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.score-value.failing {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.test-cases {
    display: grid;
    gap: 1rem;
}

.test-case {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    padding: 1rem;
    border-left: 4px solid transparent;
}

.test-case.passed {
    border-left-color: #22c55e;
}

.test-case.failed {
    border-left-color: #ef4444;
}

.test-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.test-title {
    font-weight: 600;
    color: var(--text-primary);
}

.test-detail {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.test-detail strong {
    color: var(--text-primary);
}

.test-detail code {
    background: rgba(0, 0, 0, 0.3);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: inherit;
    color: var(--primary);
}

.test-error {
    background: rgba(239, 68, 68, 0.1);
    padding: 0.5rem;
    border-radius: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #fca5a5;
}

/* Drag and Drop Styles */
.drag-drop-container {
    background: var(--dark-card);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 215, 0, 0.2);
    overflow: hidden;
    font-family: 'Inter', sans-serif;
}

.drag-drop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    padding: 1rem;
}

.question-title {
    color: var(--text-primary);
    margin: 0;
    font-size: 1.1rem;
}

.drag-drop-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-reset, .btn-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-reset {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.btn-reset:hover {
    background: rgba(255, 255, 255, 0.2);
}

.btn-check {
    background: var(--primary);
    color: black;
}

.btn-check:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-check:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.drag-drop-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 1.5rem;
    min-height: 400px;
}

.section-title {
    color: var(--text-secondary);
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.drag-items-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-height: 200px;
}

.drag-item {
    background: rgba(255, 215, 0, 0.1);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 0.5rem;
    cursor: grab;
    transition: all 0.3s ease;
    user-select: none;
}

.drag-item:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.2);
}

.drag-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    cursor: grabbing;
}

.drag-item-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
}

.drag-handle {
    color: var(--text-muted);
    font-size: 1rem;
    cursor: grab;
}

.drag-text {
    flex: 1;
    color: var(--text-primary);
    font-weight: 500;
}

.remove-item-btn {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.remove-item-btn:hover {
    background: rgba(239, 68, 68, 0.4);
    transform: scale(1.1);
}

.drop-zones-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.drop-zone {
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    min-height: 80px;
}

.drop-zone.drag-compatible {
    border-color: var(--success);
    background: rgba(34, 197, 94, 0.1);
}

.drop-zone.drag-incompatible {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.drop-zone.drag-over {
    border-color: var(--primary);
    background: rgba(255, 215, 0, 0.1);
    transform: scale(1.02);
}

.drop-zone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.zone-title {
    color: var(--text-primary);
    font-weight: 600;
}

.zone-count {
    color: var(--text-muted);
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
}

.drop-zone-content {
    padding: 0.75rem;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.drop-placeholder {
    color: var(--text-muted);
    text-align: center;
    padding: 1rem;
    font-style: italic;
    border: 1px dashed rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
}

.dropped-item {
    background: rgba(255, 215, 0, 0.15);
    border-color: var(--primary);
}

.drag-drop-feedback {
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    padding: 1.5rem;
}

.feedback-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.feedback-header h4 {
    color: var(--text-primary);
    margin: 0;
}

.score-display {
    font-weight: 700;
    font-size: 1.1rem;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
}

.score-display.passing {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.score-display.failing {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.feedback-details {
    display: grid;
    gap: 0.75rem;
}

.feedback-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
}

.feedback-item.correct {
    border-left: 4px solid #22c55e;
}

.feedback-item.incorrect {
    border-left: 4px solid #ef4444;
}

.feedback-icon {
    font-size: 1.2rem;
    margin-top: 0.1rem;
}

.feedback-content {
    flex: 1;
}

.feedback-content strong {
    color: var(--text-primary);
    display: block;
    margin-bottom: 0.25rem;
}

.feedback-details-text {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .code-editor-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .code-editor-tabs {
        justify-content: center;
    }

    .code-editor-actions {
        justify-content: center;
    }

    .test-results-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        text-align: center;
    }

    .drag-drop-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .drag-drop-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .drag-drop-actions {
        justify-content: center;
    }

    .feedback-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
