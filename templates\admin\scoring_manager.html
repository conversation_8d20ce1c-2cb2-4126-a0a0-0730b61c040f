{% extends 'admin/base_admin.html' %}

{% block title %}Scoring Manager - {{ version.name }}{% endblock %}

{% block content %}
{% csrf_token %}

<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">scoring_manager.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    🎯 Scoring Manager - {{ version.name }}
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > Advanced scoring configuration and validation
                </p>
            </div>
            <a href="{% url 'admin_panel:admin_test_version_detail' version.id %}" class="btn btn-secondary">
                🔙 Back to Questions
            </a>
        </div>
    </div>
</div>

<!-- Scoring Status -->
<div class="admin-grid admin-grid-3">
    <div class="admin-card" style="border-left: 4px solid {% if distribution.is_balanced %}var(--success){% else %}var(--error){% endif %};">
        <h3>📊 Current Status</h3>
        <div style="font-size: 2rem; color: {% if distribution.is_balanced %}var(--success){% else %}var(--error){% endif %}; font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {% if distribution.is_balanced %}✅{% else %}⚠️{% endif %}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            {% if distribution.is_balanced %}
                Scoring is balanced
            {% else %}
                Needs recalculation
            {% endif %}
        </p>
    </div>
    
    <div class="admin-card">
        <h3>📝 Normal Questions</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ distribution.normal_total }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            {{ distribution.normal_count }} questions (target: 100)
        </p>
        <div style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.5rem;">
            Range: {{ distribution.normal_min }}-{{ distribution.normal_max }} pts
        </div>
    </div>
    
    <div class="admin-card">
        <h3>🎁 Bonus Questions</h3>
        <div style="font-size: 2rem; color: var(--accent); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ distribution.bonus_total }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            {{ distribution.bonus_count }} questions (extra credit)
        </p>
        {% if distribution.bonus_count > 0 %}
        <div style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.5rem;">
            Range: {{ distribution.bonus_min }}-{{ distribution.bonus_max }} pts
        </div>
        {% endif %}
    </div>
</div>

<!-- Issues and Warnings -->
{% if issues %}
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">scoring_issues.log</div>
    </div>
    <div class="terminal-content">
        <h3 style="color: var(--error); margin-bottom: 1.5rem;">⚠️ Scoring Issues</h3>
        
        <div style="display: grid; gap: 1rem;">
            {% for issue in issues %}
            <div style="padding: 1rem; background: rgba(255, 0, 64, 0.1); border: 1px solid rgba(255, 0, 64, 0.3); border-radius: 0.5rem; border-left: 4px solid var(--error);">
                <div style="color: var(--text-primary);">{{ issue }}</div>
            </div>
            {% endfor %}
        </div>
        
        <div style="margin-top: 1.5rem;">
            <form method="post" style="display: inline;">
                {% csrf_token %}
                <input type="hidden" name="action" value="auto_fix">
                <button type="submit" class="btn btn-primary">
                    🔧 Auto-Fix Issues
                </button>
            </form>
        </div>
    </div>
</div>
{% endif %}

<!-- Scoring Actions -->
<div class="admin-grid admin-grid-2">
    <!-- Automatic Distribution -->
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">auto_distribution.py</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">🤖 Automatic Distribution</h3>
            
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="custom_distribute">
                
                <div class="form-group">
                    <label class="form-label">🎯 Target Total Points</label>
                    <input type="number" name="target_total" class="form-input" value="100" min="1" max="1000" required>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Total points for normal questions (1-1000)
                    </small>
                </div>
                
                <div style="margin-bottom: 1.5rem; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem;">
                    <h4 style="color: var(--text-secondary); margin-bottom: 1rem;">Distribution Preview:</h4>
                    <div style="font-family: 'JetBrains Mono', monospace; font-size: 0.9rem; color: var(--text-secondary);">
                        <div>• Normal questions: {{ distribution.normal_count }} × ~{{ distribution.normal_avg|floatformat:1 }} pts</div>
                        <div>• Bonus questions: {{ distribution.bonus_count }} × ~{{ distribution.bonus_avg|floatformat:1 }} pts</div>
                        <div>• Max possible score: {{ distribution.max_possible }} pts</div>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    ⚡ Redistribute Scores
                </button>
            </form>
        </div>
    </div>
    
    <!-- Manual Adjustment -->
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">manual_scores.json</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">✏️ Manual Score Adjustment</h3>
            
            <form method="post" id="manualScoresForm">
                {% csrf_token %}
                <input type="hidden" name="action" value="manual_scores">
                
                <div style="max-height: 300px; overflow-y: auto; margin-bottom: 1.5rem;">
                    {% for question in questions %}
                    <div style="display: flex; align-items: center; gap: 1rem; padding: 0.75rem; margin-bottom: 0.5rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-left: 3px solid {% if question.is_bonus %}var(--accent){% else %}var(--primary){% endif %};">
                        <div style="flex: 1; min-width: 0;">
                            <div style="color: var(--text-primary); font-weight: 500; margin-bottom: 0.25rem;">
                                {{ question.order }}. {{ question.name }}
                                {% if question.is_bonus %}
                                    <span style="background: var(--accent); color: white; padding: 0.125rem 0.25rem; border-radius: 0.125rem; font-size: 0.7rem; margin-left: 0.5rem;">BONUS</span>
                                {% endif %}
                            </div>
                            <div style="color: var(--text-muted); font-size: 0.8rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                {{ question.description|truncatechars:50 }}
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <input type="number" name="score_{{ question.id }}" value="{{ question.score }}" min="0" max="100" 
                                   class="form-input" style="width: 80px; text-align: center;">
                            <span style="color: var(--text-muted); font-size: 0.9rem;">pts</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-primary">
                        💾 Save Manual Scores
                    </button>
                    <button type="button" onclick="resetToCalculated()" class="btn btn-secondary">
                        🔄 Reset to Calculated
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Score Summary -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">score_summary.log</div>
    </div>
    <div class="terminal-content">
        <h3 style="color: var(--primary); margin-bottom: 1.5rem;">📈 Detailed Score Analysis</h3>
        
        <div class="admin-grid admin-grid-2">
            <div>
                <h4 style="color: var(--text-secondary); margin-bottom: 1rem;">Normal Questions Distribution</h4>
                <div style="font-family: 'JetBrains Mono', monospace; font-size: 0.9rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Total Points:</span>
                        <span style="color: var(--primary);">{{ distribution.normal_total }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Average per Question:</span>
                        <span style="color: var(--text-primary);">{{ distribution.normal_avg }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Min/Max Points:</span>
                        <span style="color: var(--text-secondary);">{{ distribution.normal_min }}/{{ distribution.normal_max }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Balance Status:</span>
                        <span style="color: {% if distribution.is_balanced %}var(--success){% else %}var(--error){% endif %};">
                            {% if distribution.is_balanced %}✅ Balanced{% else %}⚠️ Unbalanced{% endif %}
                        </span>
                    </div>
                </div>
            </div>
            
            <div>
                <h4 style="color: var(--text-secondary); margin-bottom: 1rem;">Bonus Questions Distribution</h4>
                {% if distribution.bonus_count > 0 %}
                <div style="font-family: 'JetBrains Mono', monospace; font-size: 0.9rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Total Bonus Points:</span>
                        <span style="color: var(--accent);">{{ distribution.bonus_total }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Average per Question:</span>
                        <span style="color: var(--text-primary);">{{ distribution.bonus_avg }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Min/Max Points:</span>
                        <span style="color: var(--text-secondary);">{{ distribution.bonus_min }}/{{ distribution.bonus_max }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Max Possible Score:</span>
                        <span style="color: var(--warning);">{{ distribution.max_possible }}</span>
                    </div>
                </div>
                {% else %}
                <div style="text-align: center; color: var(--text-muted); padding: 2rem;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎁</div>
                    <p>No bonus questions</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function resetToCalculated() {
    if (confirm('Reset all scores to automatically calculated values?')) {
        // This would trigger a recalculation and reload
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "admin_panel:admin_recalculate_scores" version.id %}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

// Real-time total calculation for manual scores
document.querySelectorAll('input[name^="score_"]').forEach(input => {
    input.addEventListener('input', function() {
        updateTotalPreview();
    });
});

function updateTotalPreview() {
    let normalTotal = 0;
    let bonusTotal = 0;
    
    document.querySelectorAll('input[name^="score_"]').forEach(input => {
        const value = parseInt(input.value) || 0;
        const row = input.closest('div');
        const isBonus = row.querySelector('.bonus') !== null;
        
        if (isBonus) {
            bonusTotal += value;
        } else {
            normalTotal += value;
        }
    });
    
    // Update preview (if we had a preview element)
    console.log(`Normal: ${normalTotal}, Bonus: ${bonusTotal}, Max: ${normalTotal + bonusTotal}`);
}
</script>
{% endblock %}
