"""
SSO Authentication Views
Handle SSO login flows and callbacks
"""

from django.shortcuts import render, redirect
from django.contrib.auth import login
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.urls import reverse
from .sso_auth import sso_manager
import logging

logger = logging.getLogger(__name__)


def sso_login(request, provider):
    """Initiate SSO login for specified provider"""
    
    sso_provider = sso_manager.get_provider(provider)
    
    if not sso_provider:
        messages.error(request, f'SSO provider "{provider}" not found')
        return redirect('login')
    
    if not sso_provider.enabled:
        messages.error(request, f'SSO provider "{provider}" is not enabled')
        return redirect('login')
    
    # Get authentication URL
    auth_url = sso_provider.get_auth_url(request)
    
    if not auth_url:
        messages.error(request, f'Failed to generate authentication URL for "{provider}"')
        return redirect('login')
    
    # Store the intended destination
    next_url = request.GET.get('next', reverse('home'))
    request.session['sso_next_url'] = next_url
    
    # Redirect to SSO provider
    return redirect(auth_url)


@csrf_exempt
@require_http_methods(["GET", "POST"])
def sso_callback(request, provider):
    """Handle SSO callback from provider"""
    
    sso_provider = sso_manager.get_provider(provider)
    
    if not sso_provider:
        messages.error(request, f'SSO provider "{provider}" not found')
        return redirect('login')
    
    # Handle the callback
    result = sso_provider.handle_callback(request)
    
    if 'error' in result:
        logger.error(f"SSO callback error for {provider}: {result['error']}")
        messages.error(request, f'SSO authentication failed: {result["error"]}')
        return redirect('login')
    
    if not result.get('success'):
        messages.error(request, 'SSO authentication failed')
        return redirect('login')
    
    # Get the authenticated user
    user = result.get('user')
    if not user:
        messages.error(request, 'Failed to create user account')
        return redirect('login')
    
    # Log the user in
    login(request, user)
    
    # Show success message
    if result.get('created'):
        messages.success(request, f'Welcome! Your account has been created using {provider.title()} SSO.')
    else:
        messages.success(request, f'Welcome back! You have been logged in using {provider.title()} SSO.')
    
    # Redirect to intended destination
    next_url = request.session.pop('sso_next_url', reverse('home'))
    return redirect(next_url)


def sso_providers_list(request):
    """API endpoint to get available SSO providers"""
    
    if request.method != 'GET':
        return JsonResponse({'error': 'GET method required'}, status=405)
    
    try:
        auth_urls = sso_manager.get_auth_urls(request)
        
        return JsonResponse({
            'success': True,
            'providers': auth_urls
        })
        
    except Exception as e:
        logger.error(f"Error getting SSO providers: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


def sso_status(request):
    """Get SSO configuration status"""
    
    if not request.user.is_staff:
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    try:
        enabled_providers = sso_manager.get_enabled_providers()
        all_providers = sso_manager.providers
        
        status = {
            'total_providers': len(all_providers),
            'enabled_providers': len(enabled_providers),
            'providers': {}
        }
        
        for name, provider in all_providers.items():
            status['providers'][name] = {
                'enabled': provider.enabled,
                'type': provider.config.get('type', 'unknown'),
                'display_name': provider.config.get('display_name', name.title()),
                'configured': bool(provider.config.get('client_id') or provider.config.get('entity_id'))
            }
        
        return JsonResponse({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        logger.error(f"Error getting SSO status: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
