/**
 * Accessibility Enhancement JavaScript
 * Provides keyboard navigation, screen reader support, and WCAG compliance
 */

class AccessibilityManager {
    constructor() {
        this.keyboardShortcuts = new Map();
        this.focusTrap = null;
        this.announcements = [];
        
        this.init();
    }
    
    init() {
        this.setupKeyboardNavigation();
        this.setupFocusManagement();
        this.setupScreenReaderSupport();
        this.setupModalAccessibility();
        this.setupFormAccessibility();
        this.addSkipLinks();
        this.setupKeyboardShortcuts();
    }
    
    setupKeyboardNavigation() {
        // Global keyboard event handler
        document.addEventListener('keydown', (e) => {
            this.handleGlobalKeydown(e);
        });
        
        // Ensure all interactive elements are keyboard accessible
        this.ensureKeyboardAccessibility();
    }
    
    handleGlobalKeydown(e) {
        // Handle keyboard shortcuts
        const shortcut = this.getShortcutKey(e);
        if (this.keyboardShortcuts.has(shortcut)) {
            e.preventDefault();
            this.keyboardShortcuts.get(shortcut)();
            return;
        }
        
        // Handle escape key for modals
        if (e.key === 'Escape') {
            this.handleEscapeKey();
        }
        
        // Handle tab navigation in modals
        if (e.key === 'Tab' && this.focusTrap) {
            this.handleTabInModal(e);
        }
    }
    
    getShortcutKey(e) {
        const modifiers = [];
        if (e.ctrlKey) modifiers.push('Ctrl');
        if (e.altKey) modifiers.push('Alt');
        if (e.shiftKey) modifiers.push('Shift');
        if (e.metaKey) modifiers.push('Meta');
        
        modifiers.push(e.key);
        return modifiers.join('+');
    }
    
    ensureKeyboardAccessibility() {
        // Add tabindex to elements that should be focusable
        const interactiveElements = document.querySelectorAll(
            'div[onclick], span[onclick], div[role="button"], span[role="button"]'
        );
        
        interactiveElements.forEach(element => {
            if (!element.hasAttribute('tabindex')) {
                element.setAttribute('tabindex', '0');
            }
            
            // Add keyboard event listeners
            element.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    element.click();
                }
            });
        });
    }
    
    setupFocusManagement() {
        // Track focus for better UX
        let lastFocusedElement = null;
        
        document.addEventListener('focusin', (e) => {
            lastFocusedElement = e.target;
        });
        
        // Store last focused element for modal restoration
        this.lastFocusedElement = lastFocusedElement;
    }
    
    setupScreenReaderSupport() {
        // Create live region for announcements
        this.createLiveRegion();
        
        // Announce page changes
        this.announcePageChange();
        
        // Announce dynamic content changes
        this.setupContentChangeAnnouncements();
    }
    
    createLiveRegion() {
        const liveRegion = document.createElement('div');
        liveRegion.id = 'live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);
        
        this.liveRegion = liveRegion;
    }
    
    announce(message, priority = 'polite') {
        if (!this.liveRegion) return;
        
        this.liveRegion.setAttribute('aria-live', priority);
        this.liveRegion.textContent = message;
        
        // Clear after announcement
        setTimeout(() => {
            this.liveRegion.textContent = '';
        }, 1000);
        
        // Store announcement
        this.announcements.push({
            message,
            timestamp: new Date(),
            priority
        });
    }
    
    announcePageChange() {
        const pageTitle = document.title;
        const mainHeading = document.querySelector('h1');
        const headingText = mainHeading ? mainHeading.textContent : '';
        
        const announcement = `Page loaded: ${pageTitle}${headingText ? '. Main heading: ' + headingText : ''}`;
        this.announce(announcement);
    }
    
    setupContentChangeAnnouncements() {
        // Observe dynamic content changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.handleNewContent(node);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    handleNewContent(element) {
        // Announce new error messages
        if (element.classList && element.classList.contains('error-message')) {
            this.announce(`Error: ${element.textContent}`, 'assertive');
        }
        
        // Announce new success messages
        if (element.classList && element.classList.contains('success-message')) {
            this.announce(`Success: ${element.textContent}`, 'polite');
        }
        
        // Announce new modal content
        if (element.classList && element.classList.contains('modal-content')) {
            const modalTitle = element.querySelector('h1, h2, h3, h4, h5, h6');
            if (modalTitle) {
                this.announce(`Modal opened: ${modalTitle.textContent}`, 'polite');
            }
        }
    }
    
    setupModalAccessibility() {
        // Handle modal opening
        document.addEventListener('click', (e) => {
            const modalTrigger = e.target.closest('[data-modal-target]');
            if (modalTrigger) {
                const modalId = modalTrigger.getAttribute('data-modal-target');
                this.openModal(modalId);
            }
        });
        
        // Handle modal closing
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close') || 
                e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });
    }
    
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;
        
        // Store last focused element
        this.lastFocusedElement = document.activeElement;
        
        // Show modal
        modal.style.display = 'flex';
        modal.setAttribute('aria-hidden', 'false');
        
        // Set up focus trap
        this.setupFocusTrap(modal);
        
        // Focus first focusable element
        const firstFocusable = this.getFocusableElements(modal)[0];
        if (firstFocusable) {
            firstFocusable.focus();
        }
        
        // Announce modal opening
        const modalTitle = modal.querySelector('h1, h2, h3, h4, h5, h6');
        if (modalTitle) {
            this.announce(`Modal opened: ${modalTitle.textContent}`);
        }
    }
    
    closeModal() {
        const openModal = document.querySelector('.modal-overlay[style*="flex"]');
        if (!openModal) return;
        
        // Hide modal
        openModal.style.display = 'none';
        openModal.setAttribute('aria-hidden', 'true');
        
        // Remove focus trap
        this.focusTrap = null;
        
        // Restore focus
        if (this.lastFocusedElement) {
            this.lastFocusedElement.focus();
        }
        
        // Announce modal closing
        this.announce('Modal closed');
    }
    
    setupFocusTrap(modal) {
        this.focusTrap = modal;
    }
    
    handleTabInModal(e) {
        if (!this.focusTrap) return;
        
        const focusableElements = this.getFocusableElements(this.focusTrap);
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }
    
    getFocusableElements(container) {
        return container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
    }
    
    handleEscapeKey() {
        // Close modal if open
        const openModal = document.querySelector('.modal-overlay[style*="flex"]');
        if (openModal) {
            this.closeModal();
            return;
        }
        
        // Close dropdown if open
        const openDropdown = document.querySelector('.dropdown.open');
        if (openDropdown) {
            openDropdown.classList.remove('open');
            return;
        }
    }
    
    setupFormAccessibility() {
        // Add ARIA attributes to form elements
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            this.enhanceFormAccessibility(form);
        });
    }
    
    enhanceFormAccessibility(form) {
        // Associate labels with inputs
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            const label = form.querySelector(`label[for="${input.id}"]`);
            if (!label && input.id) {
                // Try to find label by proximity
                const nearbyLabel = input.previousElementSibling;
                if (nearbyLabel && nearbyLabel.tagName === 'LABEL') {
                    nearbyLabel.setAttribute('for', input.id);
                }
            }
            
            // Add ARIA attributes for validation
            if (input.hasAttribute('required')) {
                input.setAttribute('aria-required', 'true');
            }
            
            // Add error message association
            const errorElement = form.querySelector(`[id="${input.id}-error"]`);
            if (errorElement) {
                input.setAttribute('aria-describedby', `${input.id}-error`);
            }
        });
    }
    
    addSkipLinks() {
        // Add skip to main content link
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.className = 'skip-link';
        skipLink.textContent = 'Skip to main content';
        
        document.body.insertBefore(skipLink, document.body.firstChild);
        
        // Ensure main content has ID
        let mainContent = document.getElementById('main-content');
        if (!mainContent) {
            mainContent = document.querySelector('main, [role="main"], .main-content');
            if (mainContent) {
                mainContent.id = 'main-content';
            }
        }
    }
    
    setupKeyboardShortcuts() {
        // Alt + M: Skip to main content
        this.keyboardShortcuts.set('Alt+m', () => {
            const mainContent = document.getElementById('main-content');
            if (mainContent) {
                mainContent.focus();
                mainContent.scrollIntoView();
            }
        });
        
        // Alt + N: Skip to navigation
        this.keyboardShortcuts.set('Alt+n', () => {
            const nav = document.querySelector('nav, [role="navigation"]');
            if (nav) {
                const firstLink = nav.querySelector('a, button');
                if (firstLink) {
                    firstLink.focus();
                }
            }
        });
        
        // Alt + H: Show keyboard shortcuts help
        this.keyboardShortcuts.set('Alt+h', () => {
            this.showKeyboardShortcuts();
        });
    }
    
    showKeyboardShortcuts() {
        const shortcuts = document.querySelector('.keyboard-shortcuts');
        if (shortcuts) {
            shortcuts.classList.toggle('show');
        } else {
            this.createKeyboardShortcutsPanel();
        }
    }
    
    createKeyboardShortcutsPanel() {
        const panel = document.createElement('div');
        panel.className = 'keyboard-shortcuts show';
        panel.innerHTML = `
            <h3>Keyboard Shortcuts</h3>
            <dl>
                <dt>Alt + M</dt>
                <dd>Skip to main content</dd>
                <dt>Alt + N</dt>
                <dd>Skip to navigation</dd>
                <dt>Alt + H</dt>
                <dd>Show/hide this help</dd>
                <dt>Tab</dt>
                <dd>Move to next element</dd>
                <dt>Shift + Tab</dt>
                <dd>Move to previous element</dd>
                <dt>Enter/Space</dt>
                <dd>Activate button or link</dd>
                <dt>Escape</dt>
                <dd>Close modal or menu</dd>
            </dl>
            <button class="btn btn-sm" onclick="this.parentElement.classList.remove('show')">Close</button>
        `;
        
        document.body.appendChild(panel);
    }
    
    // Public methods for external use
    announceToScreenReader(message, priority = 'polite') {
        this.announce(message, priority);
    }
    
    focusElement(selector) {
        const element = document.querySelector(selector);
        if (element) {
            element.focus();
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
    
    addKeyboardShortcut(key, callback) {
        this.keyboardShortcuts.set(key, callback);
    }
}

// Initialize accessibility manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.accessibilityManager = new AccessibilityManager();
});

// Utility functions for external use
window.announceToScreenReader = function(message, priority = 'polite') {
    if (window.accessibilityManager) {
        window.accessibilityManager.announceToScreenReader(message, priority);
    }
};

window.focusElement = function(selector) {
    if (window.accessibilityManager) {
        window.accessibilityManager.focusElement(selector);
    }
};
