{% extends 'admin/admin_base.html' %}

{% block title %}Create Test Version - KERNELiOS Admin{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">create_test_version.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    ➕ Create New Test Version
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > Initialize a new exam version with smart question management
                </p>
            </div>
            <a href="{% url 'admin_panel:admin_test_versions' %}" class="btn btn-secondary">
                🔙 Back to Versions
            </a>
        </div>
    </div>
</div>

<!-- Creation Form -->
<div class="admin-grid admin-grid-2">
    <!-- Main Form -->
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">version_config.json</div>
        </div>
        <div class="terminal-content">
            <form method="post" action="{% url 'admin_panel:admin_create_test_version' %}" id="createVersionForm">
                {% csrf_token %}
                
                <div class="form-group">
                    <label class="form-label">📝 Version Name *</label>
                    <input type="text" name="name" class="form-input" placeholder="e.g., V1, Midterm-2024, Advanced-Security" required>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Must be unique. Use clear, descriptive names.
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">📄 Description</label>
                    <textarea name="description" class="form-textarea" rows="4" placeholder="Brief description of this exam version, its purpose, and target audience..."></textarea>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Optional but recommended for better organization.
                    </small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">🎯 Initial Setup</label>
                    <div style="display: grid; gap: 1rem;">
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 1px solid rgba(255, 255, 255, 0.1);">
                            <input type="radio" name="setup_type" value="empty" checked style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">🆕 Start Empty</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Create an empty version and add questions manually</div>
                            </div>
                        </label>
                        
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 1px solid rgba(255, 255, 255, 0.1);">
                            <input type="radio" name="setup_type" value="template" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">📋 Use Template</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Start with predefined question templates</div>
                            </div>
                        </label>
                        
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border: 1px solid rgba(255, 255, 255, 0.1);">
                            <input type="radio" name="setup_type" value="import" style="margin: 0;">
                            <div>
                                <div style="color: var(--text-primary); font-weight: 500;">📥 Import Questions</div>
                                <div style="color: var(--text-muted); font-size: 0.8rem;">Upload questions from CSV file</div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- Import Options (hidden by default) -->
                <div id="importOptions" style="display: none;" class="form-group">
                    <label class="form-label">📁 CSV File</label>
                    <input type="file" name="csv_file" class="form-input" accept=".csv">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Format: Title, Description, Answer, points, is_bonus, question_type, choices
                    </small>
                </div>
                
                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        ✨ Create Version
                    </button>
                    <a href="{% url 'admin_panel:admin_test_versions' %}" class="btn btn-secondary">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Help & Guidelines -->
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">creation_guide.md</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">📖 Creation Guide</h3>
            
            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--text-primary); margin-bottom: 1rem;">🎯 Version Naming</h4>
                <ul style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.6; margin-left: 1.5rem;">
                    <li>Use clear, descriptive names (V1, V2, Midterm-2024)</li>
                    <li>Include difficulty level if applicable (Basic, Advanced)</li>
                    <li>Consider the target audience (Students, Professionals)</li>
                    <li>Avoid special characters and spaces in names</li>
                </ul>
            </div>
            
            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--text-primary); margin-bottom: 1rem;">📝 Question Types</h4>
                <div style="display: grid; gap: 0.75rem;">
                    <div style="padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-left: 3px solid var(--primary);">
                        <div style="color: var(--text-primary); font-weight: 500;">📝 Text Answer</div>
                        <div style="color: var(--text-muted); font-size: 0.8rem;">Single correct text answer (case-insensitive)</div>
                    </div>
                    
                    <div style="padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-left: 3px solid var(--success);">
                        <div style="color: var(--text-primary); font-weight: 500;">🔘 Multiple Choice</div>
                        <div style="color: var(--text-muted); font-size: 0.8rem;">4 options (A, B, C, D) with one correct answer</div>
                    </div>
                    
                    <div style="padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-left: 3px solid var(--warning);">
                        <div style="color: var(--text-primary); font-weight: 500;">✅ True/False</div>
                        <div style="color: var(--text-muted); font-size: 0.8rem;">Simple boolean questions</div>
                    </div>
                </div>
            </div>
            
            <div style="margin-bottom: 2rem;">
                <h4 style="color: var(--text-primary); margin-bottom: 1rem;">🎁 Scoring System</h4>
                <ul style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.6; margin-left: 1.5rem;">
                    <li><strong>Normal Questions:</strong> Always total 100 points</li>
                    <li><strong>Bonus Questions:</strong> Extra points (max score still 100)</li>
                    <li><strong>Auto-Distribution:</strong> Points divided equally among normal questions</li>
                    <li><strong>Smart Calculation:</strong> Scores update automatically when questions are added/removed</li>
                </ul>
            </div>
            
            <div style="padding: 1rem; background: rgba(255, 215, 0, 0.1); border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem;">
                <div style="color: var(--primary); font-weight: 500; margin-bottom: 0.5rem;">💡 Pro Tip</div>
                <div style="color: var(--text-secondary); font-size: 0.9rem;">
                    Start with an empty version to have full control over question creation, or use import for bulk question addition.
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Handle setup type changes
document.querySelectorAll('input[name="setup_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const importOptions = document.getElementById('importOptions');
        if (this.value === 'import') {
            importOptions.style.display = 'block';
        } else {
            importOptions.style.display = 'none';
        }
    });
});

// Form validation
document.getElementById('createVersionForm').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('Version name is required');
        return;
    }
    
    // Check for valid characters
    if (!/^[a-zA-Z0-9_-]+$/.test(name)) {
        e.preventDefault();
        alert('Version name can only contain letters, numbers, hyphens, and underscores');
        return;
    }
    
    const setupType = document.querySelector('input[name="setup_type"]:checked').value;
    if (setupType === 'import') {
        const csvFile = document.querySelector('input[name="csv_file"]').files[0];
        if (!csvFile) {
            e.preventDefault();
            alert('Please select a CSV file for import');
            return;
        }
    }
});
</script>
{% endblock %}
