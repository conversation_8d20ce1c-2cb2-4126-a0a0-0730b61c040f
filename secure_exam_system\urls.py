"""
URL configuration for secure_exam_system project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from exam import admin_views, admin_panel_views, sso_views

# Legacy admin URLs (keep for backward compatibility)
legacy_admin_patterns = [
    path('exam-dashboard/', admin_views.admin_dashboard, name='legacy_exam_dashboard'),
    path('bulk-import/', admin_views.bulk_import_questions, name='bulk_import_questions'),
    path('export-results/', admin_views.export_results, name='export_results'),
    path('email-results/', admin_views.email_results, name='email_results'),
    path('clear-data/', admin_views.clear_all_data, name='clear_all_data'),
    path('recalculate-scores/', admin_views.recalculate_scores, name='recalculate_scores'),
]

# New admin panel URLs
admin_panel_patterns = [
    path('dashboard/', admin_panel_views.admin_dashboard, name='admin_dashboard'),
    path('test-versions/', admin_panel_views.admin_test_versions, name='admin_test_versions'),
    path('test-versions/create/', admin_panel_views.admin_create_test_version, name='admin_create_test_version'),
    path('test-versions/wizard/', admin_panel_views.admin_test_version_wizard, name='admin_test_version_wizard'),
    path('test-versions/<int:version_id>/build/', admin_panel_views.admin_create_test_version_with_questions, name='admin_create_test_version_with_questions'),
    path('test-versions/<int:version_id>/', admin_panel_views.admin_test_version_detail, name='admin_test_version_detail'),
    path('test-versions/<int:version_id>/create-question/', admin_panel_views.admin_create_question, name='admin_create_question'),
    path('test-versions/<int:version_id>/edit-question/<int:question_id>/', admin_panel_views.admin_edit_question, name='admin_edit_question'),
    path('test-versions/<int:version_id>/delete-question/<int:question_id>/', admin_panel_views.admin_delete_question, name='admin_delete_question'),
    path('test-versions/<int:version_id>/recalculate-scores/', admin_panel_views.admin_recalculate_scores, name='admin_recalculate_scores'),
    path('test-versions/<int:version_id>/scoring/', admin_panel_views.admin_scoring_manager, name='admin_scoring_manager'),
    path('test-versions/<int:version_id>/export-csv/', admin_panel_views.admin_export_test_version_csv, name='admin_export_test_version_csv'),
    path('test-versions/<int:version_id>/import-csv/', admin_panel_views.admin_import_test_version_csv, name='admin_import_test_version_csv'),
    path('csv-template/', admin_panel_views.admin_download_csv_template, name='admin_download_csv_template'),
    path('instances/', admin_panel_views.admin_instances, name='admin_instances'),
    path('users/', admin_panel_views.admin_users, name='admin_users'),
    path('users/create/', admin_panel_views.admin_create_user, name='admin_create_user'),
    path('users/edit/', admin_panel_views.admin_edit_user, name='admin_edit_user'),
    path('users/api/', admin_panel_views.admin_users_api, name='admin_users_api'),
    path('users/<int:user_id>/delete/', admin_panel_views.admin_delete_user, name='admin_delete_user'),
    path('settings/', admin_panel_views.admin_settings, name='admin_settings'),
    path('settings/test-email/', admin_panel_views.admin_test_email, name='admin_test_email'),
    path('cache/', admin_panel_views.admin_cache_management, name='admin_cache_management'),
    path('cache/api/', admin_panel_views.admin_cache_api, name='admin_cache_api'),
    path('communications/', admin_panel_views.admin_communications, name='admin_communications'),
    path('communications/announcements/', admin_panel_views.admin_announcements, name='admin_announcements'),
    path('communications/email-templates/', admin_panel_views.admin_email_templates, name='admin_email_templates'),
    path('communications/chat/', admin_panel_views.admin_chat_management, name='admin_chat_management'),
    path('analytics/', admin_panel_views.admin_analytics_dashboard, name='admin_analytics_dashboard'),
    path('analytics/api/', admin_panel_views.admin_analytics_api, name='admin_analytics_api'),
    path('backups/', admin_panel_views.admin_backup_management, name='admin_backup_management'),
    path('backups/api/', admin_panel_views.admin_backup_api, name='admin_backup_api'),
    path('monitoring/', admin_panel_views.admin_monitoring_dashboard, name='admin_monitoring_dashboard'),
    path('monitoring/api/', admin_panel_views.admin_monitoring_api, name='admin_monitoring_api'),
    path('security/', admin_panel_views.admin_security_audit, name='admin_security_audit'),
    path('security/api/', admin_panel_views.admin_security_api, name='admin_security_api'),
    path('moodle/', admin_panel_views.admin_moodle_integration, name='admin_moodle_integration'),
    path('moodle/api/', admin_panel_views.admin_moodle_api, name='admin_moodle_api'),
    path('maintenance/', admin_panel_views.admin_maintenance_tools, name='admin_maintenance_tools'),
    path('maintenance/api/', admin_panel_views.admin_maintenance_api, name='admin_maintenance_api'),
    path('import-questions/', admin_panel_views.admin_import_questions, name='admin_import_questions'),
    path('export-data/', admin_panel_views.admin_export_data, name='admin_export_data'),
]

urlpatterns = [
    path('admin/', admin.site.urls),  # Django admin (keep for fallback)
    path('admin/', include((legacy_admin_patterns, 'exam_admin'))),  # Legacy admin
    path('admin-panel/', include((admin_panel_patterns, 'admin_panel'))),  # New admin panel

    # SSO Authentication URLs
    path('sso/login/<str:provider>/', sso_views.sso_login, name='sso_login'),
    path('sso/callback/<str:provider>/', sso_views.sso_callback, name='sso_callback'),
    path('sso/providers/', sso_views.sso_providers_list, name='sso_providers_list'),
    path('sso/status/', sso_views.sso_status, name='sso_status'),

    path('', include('exam.urls')),
]

# Custom error handlers
handler404 = 'exam.views.handler404'

# Serve assets during development
if settings.DEBUG:
    urlpatterns += static('/assets/', document_root='assets')
