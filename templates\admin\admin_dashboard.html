{% extends 'admin/admin_base.html' %}

{% block title %}Admin Dashboard - KERNELiOS{% endblock %}

{% block content %}
<!-- Dashboard Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">admin_dashboard.py</div>
    </div>
    <div class="terminal-content">
        <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 1rem;">
            🛡️ KERNELiOS Admin Dashboard
        </h1>
        <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
            > System administration and exam management console
        </p>
    </div>
</div>

<!-- System Statistics -->
<div class="admin-grid admin-grid-4">
    <div class="admin-card">
        <h3>📚 Test Versions</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ stats.total_versions }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Active exam versions</p>
        <a href="{% url 'admin_panel:admin_test_versions' %}" class="btn btn-secondary" style="margin-top: 1rem;">
            Manage Versions
        </a>
    </div>

    <div class="admin-card">
        <h3>❓ Questions</h3>
        <div style="font-size: 2rem; color: var(--success); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ stats.total_questions }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Total questions in system</p>
        <div style="margin-top: 1rem; font-size: 0.8rem; color: var(--text-secondary);">
            <div>📝 Text: {{ stats.text_questions }}</div>
            <div>🔘 Multiple Choice: {{ stats.mc_questions }}</div>
            <div>✅ True/False: {{ stats.tf_questions }}</div>
        </div>
    </div>

    <div class="admin-card">
        <h3>👥 Users</h3>
        <div style="font-size: 2rem; color: var(--warning); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ stats.total_users }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Registered users</p>
        <div style="margin-top: 1rem; font-size: 0.8rem; color: var(--text-secondary);">
            <div>👨‍🏫 Teachers: {{ stats.teachers }}</div>
            <div>👨‍🎓 Students: {{ stats.students }}</div>
        </div>
        <a href="{% url 'admin_panel:admin_users' %}" class="btn btn-secondary" style="margin-top: 1rem;">
            Manage Users
        </a>
    </div>

    <div class="admin-card">
        <h3>🎯 Exam Instances</h3>
        <div style="font-size: 2rem; color: var(--accent); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ stats.total_instances }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Active exam sessions</p>
        <div style="margin-top: 1rem; font-size: 0.8rem; color: var(--text-secondary);">
            <div>🟢 Active: {{ stats.active_instances }}</div>
            <div>👨‍🎓 Students: {{ stats.total_students }}</div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="admin-grid admin-grid-2">
    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">quick_actions.sh</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">⚡ Quick Actions</h3>
            
            <div style="display: grid; gap: 1rem;">
                <a href="{% url 'admin_panel:admin_test_versions' %}?action=create" class="btn btn-primary">
                    ➕ Create New Scenario Version
                </a>

                <a href="{% url 'admin_panel:admin_users' %}?action=create" class="btn btn-secondary">
                    👤 Add New User
                </a>
                
                <button onclick="showImportModal()" class="btn btn-secondary">
                    📥 Import Scenario Elements
                </button>
                
                <a href="{% url 'admin_panel:admin_export_data' %}" class="btn btn-secondary">
                    📤 Export System Data
                </a>
            </div>
        </div>
    </div>

    <div class="terminal-window">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">system_status.log</div>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">📊 System Status</h3>
            
            <div style="display: grid; gap: 1rem; font-family: 'JetBrains Mono', monospace; font-size: 0.9rem;">
                <div style="display: flex; justify-content: space-between;">
                    <span style="color: var(--text-secondary);">Database Status:</span>
                    <span style="color: var(--success);">🟢 Online</span>
                </div>
                
                <div style="display: flex; justify-content: space-between;">
                    <span style="color: var(--text-secondary);">Email Service:</span>
                    <span style="color: {% if app_config.email_host %}var(--success)">🟢 Configured{% else %}var(--warning)">⚠️ Not Configured{% endif %}</span>
                </div>
                
                <div style="display: flex; justify-content: space-between;">
                    <span style="color: var(--text-secondary);">Active Exams:</span>
                    <span style="color: var(--primary);">{{ stats.active_exams }}</span>
                </div>
                
                <div style="display: flex; justify-content: space-between;">
                    <span style="color: var(--text-secondary);">Last Export:</span>
                    <span style="color: var(--text-muted);">
                        {% if app_config.last_export_time %}
                            {{ app_config.last_export_time|date:"M d, H:i" }}
                        {% else %}
                            Never
                        {% endif %}
                    </span>
                </div>
            </div>
            
            <div style="margin-top: 1.5rem;">
                <a href="{% url 'admin_panel:admin_settings' %}" class="btn btn-secondary">
                    ⚙️ System Settings
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">recent_activity.log</div>
    </div>
    <div class="terminal-content">
        <h3 style="color: var(--primary); margin-bottom: 1.5rem;">📋 Recent Activity</h3>
        
        {% if recent_activity %}
            <div style="max-height: 300px; overflow-y: auto;">
                {% for activity in recent_activity %}
                <div style="padding: 0.75rem; margin-bottom: 0.5rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-left: 3px solid var(--primary);">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: var(--text-primary);">{{ activity.description }}</span>
                        <span style="color: var(--text-muted); font-size: 0.8rem; font-family: 'JetBrains Mono', monospace;">
                            {{ activity.timestamp|date:"M d, H:i" }}
                        </span>
                    </div>
                    {% if activity.user %}
                    <div style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.25rem;">
                        by {{ activity.user.username }}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div style="text-align: center; color: var(--text-muted); padding: 2rem;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">📝</div>
                <p>No recent activity to display</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Import Modal -->
<div id="importModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 2000; align-items: center; justify-content: center;">
    <div class="terminal-window" style="width: 90%; max-width: 500px; margin: 0;">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="terminal-dot red"></div>
                <div class="terminal-dot yellow"></div>
                <div class="terminal-dot green"></div>
            </div>
            <div class="terminal-title">import_questions.py</div>
            <button onclick="closeImportModal()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.2rem;">×</button>
        </div>
        <div class="terminal-content">
            <h3 style="color: var(--primary); margin-bottom: 1.5rem;">📥 Import Questions</h3>
            
            <form id="importForm" method="post" action="{% url 'admin_panel:admin_import_questions' %}" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="form-group">
                    <label class="form-label">Test Version:</label>
                    <select name="version_id" class="form-select" required>
                        <option value="">Select version...</option>
                        {% for version in test_versions %}
                            <option value="{{ version.id }}">{{ version.name }} - {{ version.description }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">CSV File:</label>
                    <input type="file" name="csv_file" class="form-input" accept=".csv" required>
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Format: Title, Description, Answer, points, is_bonus, question_type, choices
                    </small>
                </div>
                
                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary">Import Questions</button>
                    <button type="button" onclick="closeImportModal()" class="btn btn-secondary">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function showImportModal() {
    document.getElementById('importModal').style.display = 'flex';
}

function closeImportModal() {
    document.getElementById('importModal').style.display = 'none';
}

// Close modal on outside click
document.getElementById('importModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImportModal();
    }
});
</script>
{% endblock %}
