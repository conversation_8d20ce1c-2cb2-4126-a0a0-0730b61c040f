{% extends 'admin/admin_base.html' %}

{% block title %}Scenario Versions - KERNELiOS Control{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">scenario_versions.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    🧬 Scenario Version Management
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > Create and manage simulation scenarios with advanced element building
                </p>
            </div>
            <a href="{% url 'admin_panel:admin_create_test_version' %}" class="btn btn-primary">
                ➕ Create New Scenario
            </a>
        </div>
    </div>
</div>

<!-- Test Versions List -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">version_list.log</div>
    </div>
    <div class="terminal-content">
        {% if versions %}
            <div class="admin-grid admin-grid-2">
                {% for version in versions %}
                <div class="admin-card" style="border-left: 4px solid {% if version.is_active %}var(--success){% else %}var(--warning){% endif %};">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem; flex-wrap: wrap; gap: 1rem;">
                        <div style="flex: 1; min-width: 200px;">
                            <h3 style="color: var(--primary); margin-bottom: 0.5rem; font-family: 'Orbitron', sans-serif;">
                                {{ version.name }}
                            </h3>
                            <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem;">
                                {{ version.description|default:"No description" }}
                            </p>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            {% if version.is_active %}
                                <span class="status-badge status-success">
                                    ACTIVE
                                </span>
                            {% else %}
                                <span class="status-badge status-warning">
                                    INACTIVE
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Version Statistics -->
                    <div class="admin-grid admin-grid-4" style="margin-bottom: 1.5rem;">
                        <div class="stat-item" style="flex-direction: column; text-align: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-bottom: none;">
                            <div style="color: var(--primary); font-size: 1.5rem; font-weight: bold; font-family: 'JetBrains Mono', monospace;">{{ version.total_questions }}</div>
                            <div style="color: var(--text-muted); font-size: 0.8rem;">Total Questions</div>
                        </div>
                        <div class="stat-item" style="flex-direction: column; text-align: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-bottom: none;">
                            <div style="color: var(--success); font-size: 1.5rem; font-weight: bold; font-family: 'JetBrains Mono', monospace;">{{ version.normal_questions }}</div>
                            <div style="color: var(--text-muted); font-size: 0.8rem;">Normal Questions</div>
                        </div>
                        <div class="stat-item" style="flex-direction: column; text-align: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-bottom: none;">
                            <div style="color: var(--accent); font-size: 1.5rem; font-weight: bold; font-family: 'JetBrains Mono', monospace;">{{ version.bonus_questions }}</div>
                            <div style="color: var(--text-muted); font-size: 0.8rem;">Bonus Questions</div>
                        </div>
                        <div class="stat-item" style="flex-direction: column; text-align: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-bottom: none;">
                            <div style="color: var(--warning); font-size: 1.5rem; font-weight: bold; font-family: 'JetBrains Mono', monospace;">{{ version.total_instances }}</div>
                            <div style="color: var(--text-muted); font-size: 0.8rem;">Active Instances</div>
                        </div>
                    </div>
                    
                    <!-- Question Type Breakdown -->
                    {% with normal_count=version.normal_question_count bonus_count=version.bonus_question_count %}
                    {% if normal_count > 0 or bonus_count > 0 %}
                    <div style="margin-bottom: 1.5rem;">
                        <div style="color: var(--text-secondary); font-size: 0.8rem; margin-bottom: 0.5rem; font-family: 'JetBrains Mono', monospace;">
                            Question Breakdown:
                        </div>
                        <div style="display: flex; gap: 1rem; font-size: 0.8rem; font-family: 'JetBrains Mono', monospace;">
                            <span style="color: var(--text-primary);">📝 Normal: {{ normal_count }}</span>
                            {% if bonus_count > 0 %}
                                <span style="color: var(--accent);">🎁 Bonus: {{ bonus_count }}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    {% endwith %}
                    
                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 0.75rem; flex-wrap: wrap;">
                        <a href="{% url 'admin_panel:admin_test_version_detail' version.id %}" class="btn btn-primary" style="flex: 1; min-width: 120px;">
                            📝 Edit Questions
                        </a>
                        <button onclick="toggleVersionStatus({{ version.id }}, {{ version.is_active|yesno:'false,true' }})" 
                                class="btn {% if version.is_active %}btn-secondary{% else %}btn-primary{% endif %}" 
                                style="flex: 1; min-width: 120px;">
                            {% if version.is_active %}⏸️ Deactivate{% else %}▶️ Activate{% endif %}
                        </button>
                        <button onclick="duplicateVersion({{ version.id }})" class="btn btn-secondary" style="flex: 1; min-width: 120px;">
                            📋 Duplicate
                        </button>
                    </div>
                    
                    <!-- Metadata -->
                    <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid rgba(255, 255, 255, 0.1); font-size: 0.8rem; color: var(--text-muted); font-family: 'JetBrains Mono', monospace;">
                        Created: {{ version.created_at|date:"M d, Y H:i" }}
                        {% if version.updated_at != version.created_at %}
                            | Updated: {{ version.updated_at|date:"M d, Y H:i" }}
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div style="text-align: center; padding: 4rem 2rem; color: var(--text-muted);">
                <div style="font-size: 4rem; margin-bottom: 1rem;">📚</div>
                <h3 style="color: var(--text-secondary); margin-bottom: 1rem;">No Test Versions Found</h3>
                <p style="margin-bottom: 2rem;">Create your first test version to get started with the exam system.</p>
                <a href="{% url 'admin_panel:admin_create_test_version' %}" class="btn btn-primary">
                    ➕ Create First Version
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
{% if versions %}
<div class="admin-grid admin-grid-4">
    <div class="admin-card">
        <h3>📊 Total Versions</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ versions.count }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Active: {{ versions|length }}
        </p>
    </div>
    
    <div class="admin-card">
        <h3>❓ Total Questions</h3>
        <div style="font-size: 2rem; color: var(--success); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {% with total_questions=0 %}
                {% for version in versions %}
                    {% with total_questions=total_questions|add:version.total_questions %}{% endwith %}
                {% endfor %}
                {{ total_questions }}
            {% endwith %}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Across all versions</p>
    </div>
    
    <div class="admin-card">
        <h3>🎯 Active Instances</h3>
        <div style="font-size: 2rem; color: var(--warning); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {% with total_instances=0 %}
                {% for version in versions %}
                    {% with total_instances=total_instances|add:version.total_instances %}{% endwith %}
                {% endfor %}
                {{ total_instances }}
            {% endwith %}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Running exams</p>
    </div>
    
    <div class="admin-card">
        <h3>⚡ Quick Actions</h3>
        <div style="display: grid; gap: 0.5rem; margin-top: 1rem;">
            <button onclick="showBulkImportModal()" class="btn btn-secondary">
                📥 Bulk Import
            </button>
            <button onclick="exportAllVersions()" class="btn btn-secondary">
                📤 Export All
            </button>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
function toggleVersionStatus(versionId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this version?`)) {
        // Implementation for toggling version status
        console.log(`${action} version ${versionId}`);
        // Add AJAX call here
    }
}

function duplicateVersion(versionId) {
    if (confirm('Create a copy of this version?')) {
        // Implementation for duplicating version
        console.log(`Duplicate version ${versionId}`);
        // Add AJAX call here
    }
}

function showBulkImportModal() {
    // Implementation for bulk import modal
    console.log('Show bulk import modal');
}

function exportAllVersions() {
    // Implementation for exporting all versions
    console.log('Export all versions');
}
</script>
{% endblock %}
