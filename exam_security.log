WARNING 2025-06-18 23:02:57,565 log 27400 1988 Forbidden (CSRF token from POST incorrect.): /login/
WARNING 2025-06-28 21:11:39,858 middleware 24772 11864 User teacher accessing exam without player profile
WARNING 2025-06-28 21:17:44,066 middleware 24772 11864 User teacher accessing exam without player profile
WARNING 2025-06-28 22:04:38,224 middleware 23592 31340 User teacher accessing exam without player profile
WARNING 2025-06-28 22:08:34,059 middleware 23592 31340 User teacher accessing exam without player profile
WARNING 2025-06-28 22:17:21,759 middleware 23592 31340 User teacher accessing exam without player profile
WARNING 2025-06-29 00:08:19,200 middleware 31964 28724 User teacher accessing exam without player profile
WARNING 2025-06-29 00:08:21,112 middleware 31964 28724 User teacher accessing exam without player profile
WARNING 2025-06-29 00:08:25,059 middleware 31964 28724 User teacher accessing exam without player profile
WARNING 2025-06-29 00:25:22,280 middleware 12568 9560 User teacher accessing exam without player profile
WARNING 2025-06-29 00:25:24,271 middleware 12568 9560 User teacher accessing exam without player profile
WARNING 2025-06-29 00:26:33,131 middleware 12568 9560 User teacher accessing exam without player profile
WARNING 2025-06-29 00:58:10,626 log 30660 25736 Forbidden (CSRF token from POST incorrect.): /login/
WARNING 2025-06-29 00:58:14,148 log 30660 25736 Forbidden (CSRF token from POST incorrect.): /login/
WARNING 2025-06-30 11:48:01,126 middleware 2988 19960 User alex2 accessing exam without player profile
WARNING 2025-06-30 11:48:02,187 middleware 2988 19960 User alex2 accessing exam without player profile
WARNING 2025-06-30 11:48:02,590 middleware 2988 19960 User alex2 accessing exam without player profile
WARNING 2025-07-01 13:25:16,907 middleware 17092 20072 User kernelios accessing exam without player profile
WARNING 2025-07-01 13:25:18,084 middleware 17092 20072 User kernelios accessing exam without player profile
WARNING 2025-07-01 13:34:10,152 middleware 17092 30420 Rate limit exceeded for IP: 127.0.0.1
WARNING 2025-07-01 13:34:17,199 middleware 17092 14324 Rate limit exceeded for IP: 127.0.0.1
WARNING 2025-07-01 13:38:11,208 middleware 17836 30792 Rate limit exceeded for IP: 127.0.0.1
WARNING 2025-07-01 13:38:30,061 middleware 21896 12780 User KERNELiOS accessing exam without player profile
WARNING 2025-07-15 20:28:45,911 middleware 37980 4456 User teacher accessing exam without player profile
WARNING 2025-07-27 10:06:56,367 log 13580 21628 Forbidden (CSRF token from POST incorrect.): /login/
ERROR 2025-07-27 15:01:20,551 exception 22992 25852 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\KernelVideo\Simulator-New\venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\KernelVideo\Simulator-New\venv\lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\KernelVideo\Simulator-New\venv\lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\KernelVideo\Simulator-New\venv\lib\site-packages\django\http\request.py", line 151, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
ERROR 2025-07-27 15:01:20,771 exception 22992 25852 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\KernelVideo\Simulator-New\venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\KernelVideo\Simulator-New\venv\lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\KernelVideo\Simulator-New\venv\lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\KernelVideo\Simulator-New\venv\lib\site-packages\django\http\request.py", line 151, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-27 18:56:45,766 middleware 23684 13676 User admin accessing exam without player profile
