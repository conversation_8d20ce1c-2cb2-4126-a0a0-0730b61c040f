<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KERNELiOS Cyber Simulator - Loading</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&family=Orbitron:wght@500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #FFD700;
            --primary-light: #FFEB3B;
            --primary-dark: #FFC107;
            --primary-hover: #FFEE58;
            --secondary: #4B0082;
            --secondary-light: #6A0DAD;
            --secondary-dark: #2E004D;
            --secondary-hover: #5D00A0;
            --dark: #0A0A0A;
            --dark-lighter: #121212;
            --dark-light: #1A1A1A;
            --dark-card: #161616;
            --gray-750: #3F3F46;
            --gray-850: #27272A;
            --accent: #FF3D00;
            --accent-light: #FF5722;
            --text-primary: #FFFFFF;
            --text-secondary: #CCCCCC;
            --text-muted: #999999;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--dark);
            color: var(--text-primary);
            overflow: hidden;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            width: 100vw;
            position: relative;
        }
        
        .cyber-grid-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(255, 215, 0, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 215, 0, 0.05) 1px, transparent 1px);
            background-size: 30px 30px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }
        
        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(30px, 30px); }
        }
        
        .loading-logo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: var(--dark-lighter);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .loading-logo img {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }
        
        .loading-logo::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            border: 3px solid var(--primary);
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.05);
            }
        }
        
        .loading-title {
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            font-size: 2.5rem;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .loading-subtitle {
            font-family: 'Orbitron', sans-serif;
            font-weight: 500;
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            text-align: center;
        }
        
        .loading-text {
            font-family: 'JetBrains Mono', monospace;
            color: var(--primary);
            display: flex;
            align-items: center;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }
        
        .loading-dots {
            margin-left: 0.5rem;
        }
        
        .terminal {
            background-color: var(--dark-lighter);
            border: 1px solid var(--gray-750);
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
            width: 600px;
            max-width: 90vw;
        }
        
        .terminal-header {
            background-color: var(--dark-card);
            padding: 0.5rem 1rem;
            display: flex;
            align-items: center;
        }
        
        .terminal-dots {
            display: flex;
            gap: 0.5rem;
        }
        
        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .terminal-dot.red {
            background-color: var(--accent);
        }
        
        .terminal-dot.yellow {
            background-color: var(--primary);
        }
        
        .terminal-dot.purple {
            background-color: var(--secondary);
        }
        
        .terminal-title {
            margin-left: auto;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.8rem;
            color: var(--text-muted);
        }
        
        .terminal-body {
            padding: 1rem;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
            min-height: 200px;
        }
        
        .terminal-line {
            margin-bottom: 0.5rem;
            display: flex;
            opacity: 0;
            animation: fadeInUp 0.5s forwards;
        }
        
        .terminal-prompt {
            color: var(--primary);
            margin-right: 0.5rem;
        }
        
        .terminal-command {
            color: var(--text-primary);
        }
        
        .terminal-output {
            color: var(--text-secondary);
            padding-left: 1rem;
        }
        
        .terminal-cursor {
            display: inline-block;
            width: 8px;
            height: 16px;
            background-color: var(--primary);
            animation: blink 1s infinite;
            vertical-align: middle;
            margin-left: 2px;
        }
        
        @keyframes blink {
            0%, 49% {
                opacity: 1;
            }
            50%, 100% {
                opacity: 0;
            }
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .progress-bar {
            width: 400px;
            max-width: 80vw;
            height: 4px;
            background-color: var(--dark-card);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 2rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
            width: 0%;
            animation: progressFill 3s ease-in-out forwards;
        }
        
        @keyframes progressFill {
            to {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="cyber-grid-bg"></div>
        
        <div class="loading-logo">
            <img src="/assets/logo.png" alt="KERNELiOS Logo">
        </div>
        
        <div class="loading-title">KERNELiOS</div>
        <div class="loading-subtitle">Cyber Simulator</div>
        
        <div class="loading-text">
            INITIALIZING SYSTEM<span class="loading-dots" id="loading-dots">...</span>
        </div>
        
        <div class="terminal">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot purple"></div>
                </div>
                <div class="terminal-title">kernelios@system</div>
            </div>
            <div class="terminal-body" id="terminal-body">
                <!-- Terminal lines will be added by JavaScript -->
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
    </div>

    <script>
        // Loading dots animation
        const loadingDots = document.getElementById('loading-dots');
        let dotsCount = 0;
        
        function animateDots() {
            dotsCount = (dotsCount + 1) % 4;
            loadingDots.textContent = '.'.repeat(dotsCount);
        }
        
        setInterval(animateDots, 500);
        
        // Terminal animation
        const terminalBody = document.getElementById('terminal-body');
        const terminalLines = [
            { type: 'command', text: 'kernelios@system:~$ sudo systemctl start cyber-simulator' },
            { type: 'output', text: '✓ Starting KERNELiOS Cyber Simulator...' },
            { type: 'output', text: '✓ Loading security modules...' },
            { type: 'output', text: '✓ Initializing threat detection engine...' },
            { type: 'output', text: '✓ Configuring virtual lab environment...' },
            { type: 'output', text: '✓ Establishing secure connections...' },
            { type: 'output', text: '✓ Loading scenario database...' },
            { type: 'output', text: '✓ Preparing user interface...' },
            { type: 'command', text: 'kernelios@system:~$ status --all' },
            { type: 'output', text: 'System Status: READY' },
            { type: 'output', text: 'Security Level: MAXIMUM' },
            { type: 'output', text: 'Exam Mode: ACTIVE' }
        ];
        
        let lineIndex = 0;
        
        function addTerminalLine() {
            if (lineIndex < terminalLines.length) {
                const line = terminalLines[lineIndex];
                const lineElement = document.createElement('div');
                lineElement.className = 'terminal-line';
                lineElement.style.animationDelay = `${lineIndex * 0.3}s`;
                
                if (line.type === 'command') {
                    lineElement.innerHTML = `
                        <span class="terminal-prompt">kernelios@system:~$</span>
                        <span class="terminal-command">${line.text.replace('kernelios@system:~$ ', '')}</span>
                    `;
                } else {
                    lineElement.innerHTML = `<span class="terminal-output">${line.text}</span>`;
                }
                
                terminalBody.appendChild(lineElement);
                lineIndex++;
                
                setTimeout(addTerminalLine, 300);
            } else {
                // Add cursor at the end
                setTimeout(() => {
                    const cursorLine = document.createElement('div');
                    cursorLine.className = 'terminal-line';
                    cursorLine.innerHTML = `
                        <span class="terminal-prompt">kernelios@system:~$</span>
                        <span class="terminal-cursor"></span>
                    `;
                    terminalBody.appendChild(cursorLine);
                }, 500);
            }
        }
        
        // Start terminal animation after a short delay
        setTimeout(addTerminalLine, 1000);
        
        // Redirect to home page after loading
        setTimeout(() => {
            window.location.href = '/';
        }, 5000);
    </script>
</body>
</html>
