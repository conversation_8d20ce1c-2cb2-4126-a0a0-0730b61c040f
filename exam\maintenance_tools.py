"""
Monitoring and Maintenance Tools
Provides application monitoring, automated testing, and maintenance capabilities
"""

import os
import subprocess
import json
import time
from datetime import datetime, timedelta
from django.conf import settings
from django.core.management import call_command
from django.core.cache import cache
from django.db import connection
from django.utils import timezone
from django.test.utils import get_runner
from django.test import TestCase
from .models import User, TestVersion, ExamInstance, Player
import logging

logger = logging.getLogger(__name__)


class MaintenanceManager:
    """Main maintenance management class"""
    
    def __init__(self):
        self.maintenance_mode_key = 'system_maintenance_mode'
        self.maintenance_message_key = 'system_maintenance_message'
    
    def enable_maintenance_mode(self, message="System is under maintenance. Please try again later."):
        """Enable maintenance mode"""
        
        cache.set(self.maintenance_mode_key, True, timeout=None)
        cache.set(self.maintenance_message_key, message, timeout=None)
        
        logger.info("Maintenance mode enabled")
        return True
    
    def disable_maintenance_mode(self):
        """Disable maintenance mode"""
        
        cache.delete(self.maintenance_mode_key)
        cache.delete(self.maintenance_message_key)
        
        logger.info("Maintenance mode disabled")
        return True
    
    def is_maintenance_mode(self):
        """Check if maintenance mode is enabled"""
        return cache.get(self.maintenance_mode_key, False)
    
    def get_maintenance_message(self):
        """Get maintenance mode message"""
        return cache.get(self.maintenance_message_key, "System is under maintenance.")
    
    def get_system_status(self):
        """Get comprehensive system status"""
        
        status = {
            'timestamp': timezone.now().isoformat(),
            'maintenance_mode': self.is_maintenance_mode(),
            'database': self._check_database(),
            'cache': self._check_cache(),
            'disk_space': self._check_disk_space(),
            'memory_usage': self._check_memory_usage(),
            'active_sessions': self._count_active_sessions(),
            'recent_errors': self._get_recent_errors(),
            'uptime': self._get_uptime()
        }
        
        return status
    
    def _check_database(self):
        """Check database connectivity and performance"""
        
        try:
            start_time = time.time()
            
            # Test basic query
            User.objects.count()
            
            query_time = (time.time() - start_time) * 1000
            
            # Check for long-running queries
            with connection.cursor() as cursor:
                # This is SQLite specific - adjust for other databases
                cursor.execute("PRAGMA database_list")
                db_info = cursor.fetchall()
            
            return {
                'status': 'healthy',
                'response_time_ms': round(query_time, 2),
                'connection_count': len(connection.queries),
                'database_info': str(db_info) if db_info else 'Unknown'
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _check_cache(self):
        """Check cache connectivity and performance"""
        
        try:
            start_time = time.time()
            
            # Test cache operations
            test_key = 'health_check_test'
            test_value = 'test_value'
            
            cache.set(test_key, test_value, 60)
            retrieved_value = cache.get(test_key)
            cache.delete(test_key)
            
            response_time = (time.time() - start_time) * 1000
            
            if retrieved_value != test_value:
                raise Exception("Cache value mismatch")
            
            return {
                'status': 'healthy',
                'response_time_ms': round(response_time, 2),
                'backend': str(cache.__class__.__name__)
            }
            
        except Exception as e:
            logger.error(f"Cache health check failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _check_disk_space(self):
        """Check available disk space"""
        
        try:
            import shutil
            
            # Check main directory
            total, used, free = shutil.disk_usage(settings.BASE_DIR)
            
            # Convert to GB
            total_gb = total / (1024**3)
            used_gb = used / (1024**3)
            free_gb = free / (1024**3)
            usage_percent = (used / total) * 100
            
            status = 'healthy'
            if usage_percent > 90:
                status = 'critical'
            elif usage_percent > 80:
                status = 'warning'
            
            return {
                'status': status,
                'total_gb': round(total_gb, 2),
                'used_gb': round(used_gb, 2),
                'free_gb': round(free_gb, 2),
                'usage_percent': round(usage_percent, 2)
            }
            
        except Exception as e:
            logger.error(f"Disk space check failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _check_memory_usage(self):
        """Check memory usage"""
        
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            
            status = 'healthy'
            if memory.percent > 90:
                status = 'critical'
            elif memory.percent > 80:
                status = 'warning'
            
            return {
                'status': status,
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'used_percent': round(memory.percent, 2)
            }
            
        except ImportError:
            return {
                'status': 'unavailable',
                'error': 'psutil not available'
            }
        except Exception as e:
            logger.error(f"Memory check failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _count_active_sessions(self):
        """Count active user sessions"""
        
        try:
            # Count active exam sessions
            active_exams = Player.objects.filter(
                start_time__isnull=False,
                end_time__isnull=True
            ).count()
            
            # Count recent logins (last hour)
            one_hour_ago = timezone.now() - timedelta(hours=1)
            recent_logins = User.objects.filter(
                last_login__gte=one_hour_ago
            ).count()
            
            return {
                'active_exams': active_exams,
                'recent_logins': recent_logins
            }
            
        except Exception as e:
            logger.error(f"Session count failed: {str(e)}")
            return {
                'active_exams': 0,
                'recent_logins': 0,
                'error': str(e)
            }
    
    def _get_recent_errors(self):
        """Get recent error logs"""
        
        try:
            # This would read from log files in a real implementation
            # For now, return placeholder data
            
            return {
                'error_count_24h': 0,
                'warning_count_24h': 0,
                'last_error': None
            }
            
        except Exception as e:
            logger.error(f"Error log check failed: {str(e)}")
            return {
                'error_count_24h': 'unknown',
                'warning_count_24h': 'unknown',
                'error': str(e)
            }
    
    def _get_uptime(self):
        """Get system uptime"""
        
        try:
            # This is a simplified uptime calculation
            # In production, you might track this differently
            
            uptime_file = os.path.join(settings.BASE_DIR, '.uptime')
            
            if os.path.exists(uptime_file):
                with open(uptime_file, 'r') as f:
                    start_time = datetime.fromisoformat(f.read().strip())
                
                uptime = timezone.now() - timezone.make_aware(start_time)
                
                return {
                    'uptime_seconds': int(uptime.total_seconds()),
                    'uptime_human': str(uptime).split('.')[0],  # Remove microseconds
                    'started_at': start_time.isoformat()
                }
            else:
                # Create uptime file
                with open(uptime_file, 'w') as f:
                    f.write(timezone.now().isoformat())
                
                return {
                    'uptime_seconds': 0,
                    'uptime_human': '0:00:00',
                    'started_at': timezone.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Uptime check failed: {str(e)}")
            return {
                'uptime_seconds': 'unknown',
                'uptime_human': 'unknown',
                'error': str(e)
            }


class AutomatedTester:
    """Automated testing system"""
    
    def __init__(self):
        self.test_results = {}
    
    def run_health_tests(self):
        """Run basic health tests"""
        
        tests = {
            'database_connectivity': self._test_database_connectivity,
            'cache_functionality': self._test_cache_functionality,
            'user_authentication': self._test_user_authentication,
            'exam_creation': self._test_exam_creation,
            'question_handling': self._test_question_handling
        }
        
        results = {
            'timestamp': timezone.now().isoformat(),
            'total_tests': len(tests),
            'passed': 0,
            'failed': 0,
            'tests': {}
        }
        
        for test_name, test_func in tests.items():
            try:
                start_time = time.time()
                test_result = test_func()
                execution_time = (time.time() - start_time) * 1000
                
                results['tests'][test_name] = {
                    'status': 'passed' if test_result.get('success', False) else 'failed',
                    'execution_time_ms': round(execution_time, 2),
                    'details': test_result
                }
                
                if test_result.get('success', False):
                    results['passed'] += 1
                else:
                    results['failed'] += 1
                    
            except Exception as e:
                results['tests'][test_name] = {
                    'status': 'error',
                    'execution_time_ms': 0,
                    'error': str(e)
                }
                results['failed'] += 1
        
        return results
    
    def _test_database_connectivity(self):
        """Test database connectivity"""
        
        try:
            # Test basic operations
            user_count = User.objects.count()
            test_version_count = TestVersion.objects.count()
            
            return {
                'success': True,
                'user_count': user_count,
                'test_version_count': test_version_count
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_cache_functionality(self):
        """Test cache functionality"""
        
        try:
            test_key = 'automated_test_key'
            test_value = {'test': 'data', 'timestamp': timezone.now().isoformat()}
            
            # Set, get, and delete
            cache.set(test_key, test_value, 60)
            retrieved = cache.get(test_key)
            cache.delete(test_key)
            
            if retrieved != test_value:
                raise Exception("Cache value mismatch")
            
            return {
                'success': True,
                'message': 'Cache operations successful'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_user_authentication(self):
        """Test user authentication system"""
        
        try:
            # Check if we can query users
            active_users = User.objects.filter(is_active=True).count()
            staff_users = User.objects.filter(is_staff=True).count()
            
            return {
                'success': True,
                'active_users': active_users,
                'staff_users': staff_users
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_exam_creation(self):
        """Test exam instance creation"""
        
        try:
            # Check if we can query exam instances
            active_instances = ExamInstance.objects.filter(is_active=True).count()
            total_instances = ExamInstance.objects.count()
            
            return {
                'success': True,
                'active_instances': active_instances,
                'total_instances': total_instances
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_question_handling(self):
        """Test question handling"""
        
        try:
            # Check if we can query test versions and levels
            test_versions = TestVersion.objects.count()
            
            # Count questions across all versions
            total_questions = 0
            for version in TestVersion.objects.all():
                total_questions += version.levels.count()
            
            return {
                'success': True,
                'test_versions': test_versions,
                'total_questions': total_questions
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


class VersionManager:
    """Version control and deployment management"""
    
    def __init__(self):
        self.version_file = os.path.join(settings.BASE_DIR, 'VERSION')
    
    def get_current_version(self):
        """Get current application version"""
        
        try:
            if os.path.exists(self.version_file):
                with open(self.version_file, 'r') as f:
                    return f.read().strip()
            else:
                return 'unknown'
                
        except Exception as e:
            logger.error(f"Error reading version: {str(e)}")
            return 'error'
    
    def set_version(self, version):
        """Set application version"""
        
        try:
            with open(self.version_file, 'w') as f:
                f.write(version)
            
            logger.info(f"Version set to: {version}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting version: {str(e)}")
            return False
    
    def get_git_info(self):
        """Get Git repository information"""
        
        try:
            # Get current commit hash
            commit_hash = subprocess.check_output(
                ['git', 'rev-parse', 'HEAD'],
                cwd=settings.BASE_DIR,
                stderr=subprocess.DEVNULL
            ).decode().strip()
            
            # Get current branch
            branch = subprocess.check_output(
                ['git', 'rev-parse', '--abbrev-ref', 'HEAD'],
                cwd=settings.BASE_DIR,
                stderr=subprocess.DEVNULL
            ).decode().strip()
            
            # Get last commit message
            commit_message = subprocess.check_output(
                ['git', 'log', '-1', '--pretty=%B'],
                cwd=settings.BASE_DIR,
                stderr=subprocess.DEVNULL
            ).decode().strip()
            
            return {
                'commit_hash': commit_hash[:8],  # Short hash
                'branch': branch,
                'commit_message': commit_message,
                'available': True
            }
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            return {
                'available': False,
                'error': 'Git not available or not a git repository'
            }


# Global instances
maintenance_manager = MaintenanceManager()
automated_tester = AutomatedTester()
version_manager = VersionManager()
