{% extends 'admin/base_admin.html' %}

{% block title %}System Monitoring - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">📊</span>
            System Monitoring Dashboard
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            Real-time system health and performance monitoring
        </p>
    </div>
    
    <div class="admin-header-actions">
        <button onclick="refreshMetrics()" class="btn btn-secondary">
            🔄 Refresh
        </button>
        
        <button onclick="toggleAutoRefresh()" id="autoRefreshBtn" class="btn btn-primary">
            ▶️ Auto Refresh
        </button>
    </div>
</div>

<div class="admin-content">
    <!-- Health Status Overview -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0;">💓 System Health Status</h3>
        </div>
        
        <div class="health-overview">
            <div class="health-status {{ health_status.overall_status }}">
                <div class="health-icon">
                    {% if health_status.overall_status == 'healthy' %}✅
                    {% elif health_status.overall_status == 'warning' %}⚠️
                    {% else %}❌{% endif %}
                </div>
                <div class="health-text">{{ health_status.overall_status|title }}</div>
            </div>
            
            <div class="health-checks">
                {% for check_name, check_data in health_status.checks.items %}
                    <div class="health-check {{ check_data.status }}">
                        <div class="check-name">{{ check_name|title }}</div>
                        <div class="check-status">{{ check_data.status|title }}</div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- System Metrics -->
    <div class="admin-grid admin-grid-2">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">🖥️ System Resources</h3>
            </div>
            
            {% if system_metrics %}
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">CPU Usage</div>
                        <div class="metric-value">{{ system_metrics.cpu.usage_percent|floatformat:1 }}%</div>
                        <div class="metric-bar">
                            <div class="metric-fill" style="width: {{ system_metrics.cpu.usage_percent }}%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-label">Memory Usage</div>
                        <div class="metric-value">{{ system_metrics.memory.usage_percent|floatformat:1 }}%</div>
                        <div class="metric-bar">
                            <div class="metric-fill" style="width: {{ system_metrics.memory.usage_percent }}%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-label">Disk Usage</div>
                        <div class="metric-value">{{ system_metrics.disk.usage_percent|floatformat:1 }}%</div>
                        <div class="metric-bar">
                            <div class="metric-fill" style="width: {{ system_metrics.disk.usage_percent }}%"></div>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="error-message">Unable to load system metrics</div>
            {% endif %}
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">🗄️ Database Performance</h3>
            </div>
            
            {% if database_metrics %}
                <div class="db-metrics">
                    <div class="db-metric">
                        <span class="metric-label">Engine:</span>
                        <span class="metric-value">{{ database_metrics.engine }}</span>
                    </div>
                    
                    <div class="db-metric">
                        <span class="metric-label">Connections:</span>
                        <span class="metric-value">{{ database_metrics.connection_count }}</span>
                    </div>
                    
                    <div class="db-metric">
                        <span class="metric-label">Query Count:</span>
                        <span class="metric-value">{{ database_metrics.query_count }}</span>
                    </div>
                    
                    <div class="db-metric">
                        <span class="metric-label">Avg Query Time:</span>
                        <span class="metric-value">{{ database_metrics.average_query_time|floatformat:3 }}s</span>
                    </div>
                </div>
            {% else %}
                <div class="error-message">Unable to load database metrics</div>
            {% endif %}
        </div>
    </div>

    <!-- Application Metrics -->
    <div class="admin-grid admin-grid-3">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📱 Application</h3>
            </div>
            
            {% if application_metrics %}
                <div class="app-metrics">
                    <div class="app-metric">
                        <div class="metric-value">{{ application_metrics.active_exam_sessions }}</div>
                        <div class="metric-label">Active Exams</div>
                    </div>
                    
                    <div class="app-metric">
                        <div class="metric-value">{{ application_metrics.recent_user_logins }}</div>
                        <div class="metric-label">Recent Logins</div>
                    </div>
                    
                    <div class="app-metric">
                        <div class="metric-value">{{ application_metrics.error_rate_percent|floatformat:1 }}%</div>
                        <div class="metric-label">Error Rate</div>
                    </div>
                </div>
            {% endif %}
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">⚡ Cache</h3>
            </div>
            
            {% if cache_metrics %}
                <div class="cache-metrics">
                    <div class="cache-status {{ cache_metrics.cache_available|yesno:'healthy,error' }}">
                        {% if cache_metrics.cache_available %}
                            ✅ Available
                        {% else %}
                            ❌ Unavailable
                        {% endif %}
                    </div>
                    
                    <div class="cache-metric">
                        <span class="metric-label">Response Time:</span>
                        <span class="metric-value">{{ cache_metrics.cache_response_time_ms|floatformat:1 }}ms</span>
                    </div>
                </div>
            {% endif %}
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">🔧 Actions</h3>
            </div>
            
            <div class="action-buttons">
                <button onclick="resetCounters()" class="btn btn-secondary btn-sm">
                    🔄 Reset Counters
                </button>
                
                <button onclick="openBackups()" class="btn btn-secondary btn-sm">
                    💾 Backups
                </button>
                
                <button onclick="openSecurity()" class="btn btn-secondary btn-sm">
                    🔒 Security
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.health-overview {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: center;
}

.health-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    border-radius: 0.75rem;
    min-width: 150px;
}

.health-status.healthy {
    background: rgba(34, 197, 94, 0.1);
    border: 2px solid rgba(34, 197, 94, 0.3);
}

.health-status.warning {
    background: rgba(251, 191, 36, 0.1);
    border: 2px solid rgba(251, 191, 36, 0.3);
}

.health-status.critical {
    background: rgba(239, 68, 68, 0.1);
    border: 2px solid rgba(239, 68, 68, 0.3);
}

.health-icon {
    font-size: 3rem;
}

.health-text {
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
}

.health-checks {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.health-check {
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
}

.health-check.healthy {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.health-check.warning {
    background: rgba(251, 191, 36, 0.1);
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.health-check.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.check-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.check-status {
    font-size: 0.9rem;
    text-transform: uppercase;
}

.metrics-grid {
    display: grid;
    gap: 1.5rem;
}

.metric-item {
    display: grid;
    gap: 0.5rem;
}

.metric-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.metric-value {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.metric-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #22c55e, #fbbf24, #ef4444);
    transition: width 0.3s ease;
}

.db-metrics {
    display: grid;
    gap: 1rem;
}

.db-metric {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.app-metrics {
    display: grid;
    gap: 1rem;
}

.app-metric {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.cache-metrics {
    display: grid;
    gap: 1rem;
}

.cache-status {
    text-align: center;
    padding: 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
}

.cache-metric {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.action-buttons {
    display: grid;
    gap: 0.5rem;
}

.error-message {
    color: #ef4444;
    text-align: center;
    padding: 2rem;
    font-style: italic;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let autoRefreshInterval = null;
let isAutoRefreshing = false;

function refreshMetrics() {
    // Reload the page to get fresh metrics
    window.location.reload();
}

function toggleAutoRefresh() {
    const btn = document.getElementById('autoRefreshBtn');
    
    if (isAutoRefreshing) {
        clearInterval(autoRefreshInterval);
        isAutoRefreshing = false;
        btn.innerHTML = '▶️ Auto Refresh';
        btn.classList.remove('btn-warning');
        btn.classList.add('btn-primary');
    } else {
        autoRefreshInterval = setInterval(refreshMetrics, 30000); // 30 seconds
        isAutoRefreshing = true;
        btn.innerHTML = '⏸️ Stop Auto Refresh';
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-warning');
    }
}

async function resetCounters() {
    try {
        const response = await fetch('{% url "admin_panel:admin_monitoring_api" %}?type=reset_counters');
        const result = await response.json();
        
        if (result.success) {
            alert('Performance counters reset successfully');
            refreshMetrics();
        } else {
            alert('Failed to reset counters: ' + result.error);
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
}

function openBackups() {
    window.open('{% url "admin_panel:admin_backup_management" %}', '_blank');
}

function openSecurity() {
    window.open('{% url "admin_panel:admin_security_audit" %}', '_blank');
}
</script>
{% endblock %}
