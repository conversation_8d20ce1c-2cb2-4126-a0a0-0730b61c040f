# Generated by Django 5.0.14 on 2025-07-27 10:22

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("exam", "0008_appconfig_require_email_verification"),
    ]

    operations = [
        migrations.AddField(
            model_name="level",
            name="correct_matches",
            field=models.JSONField(
                blank=True,
                help_text="Correct item-zone pairs for drag-and-drop questions",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="level",
            name="drag_items",
            field=models.JSONField(
                blank=True,
                help_text="Items to drag in drag-and-drop questions",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="level",
            name="drop_zones",
            field=models.J<PERSON><PERSON>ield(
                blank=True,
                help_text="Drop zones/targets for drag-and-drop questions",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="level",
            name="programming_language",
            field=models.CharField(
                blank=True,
                default="python",
                help_text="Programming language for code execution questions",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="level",
            name="starter_code",
            field=models.TextField(
                blank=True, help_text="Initial code template for students"
            ),
        ),
        migrations.AddField(
            model_name="level",
            name="test_cases",
            field=models.JSONField(
                blank=True,
                help_text="Test cases for auto-grading code execution questions",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="player",
            name="anonymized_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="player",
            name="data_retention_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="player",
            name="is_anonymized",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="appconfig",
            name="email_host_password",
            field=models.CharField(blank=True, max_length=500),
        ),
        migrations.AlterField(
            model_name="level",
            name="question_type",
            field=models.CharField(
                choices=[
                    ("text", "Text Answer"),
                    ("multiple_choice", "Multiple Choice"),
                    ("true_false", "True/False"),
                    ("code_execution", "Code Execution"),
                    ("drag_drop", "Drag and Drop"),
                ],
                default="text",
                help_text="Type of question",
                max_length=20,
            ),
        ),
    ]
