"""
WCAG 2.1 Accessibility Utilities
Provides accessibility compliance tools and helpers
"""

from django.utils.safestring import mark_safe
from django.template import Library
import re

register = Library()


class AccessibilityHelper:
    """Helper class for accessibility features"""
    
    @staticmethod
    def generate_aria_label(text, context=""):
        """Generate appropriate ARIA label"""
        if context:
            return f"{text} - {context}"
        return text
    
    @staticmethod
    def generate_skip_link(target_id, text="Skip to main content"):
        """Generate skip navigation link"""
        return mark_safe(f'<a href="#{target_id}" class="skip-link">{text}</a>')
    
    @staticmethod
    def generate_landmark_role(element_type):
        """Generate appropriate landmark role"""
        landmarks = {
            'header': 'banner',
            'nav': 'navigation',
            'main': 'main',
            'aside': 'complementary',
            'footer': 'contentinfo',
            'section': 'region'
        }
        return landmarks.get(element_type, '')
    
    @staticmethod
    def generate_heading_structure(level, text, id_attr=None):
        """Generate properly structured heading"""
        if not 1 <= level <= 6:
            level = 2  # Default to h2
        
        id_part = f' id="{id_attr}"' if id_attr else ''
        return mark_safe(f'<h{level}{id_part}>{text}</h{level}>')
    
    @staticmethod
    def generate_form_label(field_id, label_text, required=False):
        """Generate accessible form label"""
        required_indicator = ' <span class="required-indicator" aria-label="required">*</span>' if required else ''
        return mark_safe(f'<label for="{field_id}" class="form-label">{label_text}{required_indicator}</label>')
    
    @staticmethod
    def generate_error_message(field_id, error_text):
        """Generate accessible error message"""
        return mark_safe(f'<div id="{field_id}-error" class="error-message" role="alert" aria-live="polite">{error_text}</div>')
    
    @staticmethod
    def generate_button(text, button_type="button", disabled=False, aria_label=None):
        """Generate accessible button"""
        disabled_attr = ' disabled' if disabled else ''
        aria_label_attr = f' aria-label="{aria_label}"' if aria_label else ''
        
        return mark_safe(f'<button type="{button_type}"{disabled_attr}{aria_label_attr} class="btn">{text}</button>')
    
    @staticmethod
    def generate_table_headers(headers):
        """Generate accessible table headers"""
        header_html = ""
        for header in headers:
            scope = 'scope="col"'
            header_html += f'<th {scope}>{header}</th>'
        return mark_safe(header_html)
    
    @staticmethod
    def check_color_contrast(foreground, background):
        """Check color contrast ratio (simplified)"""
        # This is a simplified version - in production, use a proper color contrast library
        # Returns True if contrast is sufficient
        return True  # Placeholder
    
    @staticmethod
    def generate_focus_indicator():
        """Generate CSS for focus indicators"""
        return """
        .focus-visible:focus,
        button:focus-visible,
        input:focus-visible,
        select:focus-visible,
        textarea:focus-visible,
        a:focus-visible {
            outline: 3px solid var(--primary);
            outline-offset: 2px;
            box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
        }
        
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary);
            color: black;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 9999;
            font-weight: bold;
        }
        
        .skip-link:focus {
            top: 6px;
        }
        """


class ScreenReaderHelper:
    """Helper for screen reader optimization"""
    
    @staticmethod
    def generate_sr_only_text(text):
        """Generate screen reader only text"""
        return mark_safe(f'<span class="sr-only">{text}</span>')
    
    @staticmethod
    def generate_live_region(text, politeness="polite"):
        """Generate live region for dynamic content"""
        return mark_safe(f'<div aria-live="{politeness}" class="sr-only">{text}</div>')
    
    @staticmethod
    def generate_progress_announcement(current, total, task=""):
        """Generate progress announcement for screen readers"""
        percentage = (current / total * 100) if total > 0 else 0
        task_text = f" {task}" if task else ""
        return f"Progress{task_text}: {current} of {total} completed, {percentage:.0f}%"
    
    @staticmethod
    def generate_table_summary(rows, columns, description=""):
        """Generate table summary for screen readers"""
        summary = f"Table with {rows} rows and {columns} columns"
        if description:
            summary += f". {description}"
        return summary


class KeyboardNavigationHelper:
    """Helper for keyboard navigation"""
    
    @staticmethod
    def generate_tabindex(value=0):
        """Generate appropriate tabindex"""
        return f'tabindex="{value}"'
    
    @staticmethod
    def generate_keyboard_shortcuts():
        """Generate keyboard shortcut information"""
        shortcuts = {
            'Alt + M': 'Skip to main content',
            'Alt + N': 'Skip to navigation',
            'Tab': 'Move to next element',
            'Shift + Tab': 'Move to previous element',
            'Enter/Space': 'Activate button or link',
            'Escape': 'Close modal or cancel action',
            'Arrow keys': 'Navigate within components'
        }
        return shortcuts
    
    @staticmethod
    def generate_modal_trap_script():
        """Generate JavaScript for modal focus trap"""
        return """
        function trapFocus(element) {
            const focusableElements = element.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];
            
            element.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    if (e.shiftKey) {
                        if (document.activeElement === firstElement) {
                            lastElement.focus();
                            e.preventDefault();
                        }
                    } else {
                        if (document.activeElement === lastElement) {
                            firstElement.focus();
                            e.preventDefault();
                        }
                    }
                }
                
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
            
            firstElement.focus();
        }
        """


class AccessibilityValidator:
    """Validate accessibility compliance"""
    
    @staticmethod
    def validate_heading_structure(html_content):
        """Validate heading hierarchy"""
        headings = re.findall(r'<h([1-6])[^>]*>', html_content)
        issues = []
        
        if not headings:
            issues.append("No headings found")
            return issues
        
        # Check if starts with h1
        if headings[0] != '1':
            issues.append("Page should start with h1")
        
        # Check for skipped levels
        prev_level = 0
        for heading in headings:
            level = int(heading)
            if level > prev_level + 1:
                issues.append(f"Heading level skipped: h{prev_level} to h{level}")
            prev_level = level
        
        return issues
    
    @staticmethod
    def validate_form_labels(html_content):
        """Validate form label associations"""
        issues = []
        
        # Find inputs without labels
        inputs = re.findall(r'<input[^>]*id="([^"]*)"[^>]*>', html_content)
        labels = re.findall(r'<label[^>]*for="([^"]*)"[^>]*>', html_content)
        
        for input_id in inputs:
            if input_id not in labels:
                issues.append(f"Input with id '{input_id}' has no associated label")
        
        return issues
    
    @staticmethod
    def validate_alt_text(html_content):
        """Validate image alt text"""
        issues = []
        
        # Find images without alt text
        images_without_alt = re.findall(r'<img(?![^>]*alt=)[^>]*>', html_content)
        if images_without_alt:
            issues.append(f"Found {len(images_without_alt)} images without alt text")
        
        # Find images with empty alt text (decorative images should have alt="")
        empty_alt_images = re.findall(r'<img[^>]*alt=""[^>]*>', html_content)
        
        return issues
    
    @staticmethod
    def validate_color_contrast(css_content):
        """Validate color contrast (simplified)"""
        issues = []
        
        # This would require a more sophisticated implementation
        # For now, return placeholder
        issues.append("Color contrast validation requires manual review")
        
        return issues
    
    @staticmethod
    def generate_accessibility_report(html_content, css_content=""):
        """Generate comprehensive accessibility report"""
        report = {
            'heading_structure': AccessibilityValidator.validate_heading_structure(html_content),
            'form_labels': AccessibilityValidator.validate_form_labels(html_content),
            'alt_text': AccessibilityValidator.validate_alt_text(html_content),
            'color_contrast': AccessibilityValidator.validate_color_contrast(css_content)
        }
        
        total_issues = sum(len(issues) for issues in report.values())
        report['summary'] = {
            'total_issues': total_issues,
            'compliance_level': 'AA' if total_issues == 0 else 'Partial',
            'recommendations': [
                'Review heading structure for logical hierarchy',
                'Ensure all form inputs have associated labels',
                'Provide meaningful alt text for images',
                'Verify color contrast meets WCAG AA standards',
                'Test with screen readers',
                'Verify keyboard navigation works properly'
            ]
        }
        
        return report


# Global instances
accessibility_helper = AccessibilityHelper()
screen_reader_helper = ScreenReaderHelper()
keyboard_helper = KeyboardNavigationHelper()
accessibility_validator = AccessibilityValidator()


# Template tags for accessibility
@register.simple_tag
def aria_label(text, context=""):
    """Template tag for ARIA labels"""
    return accessibility_helper.generate_aria_label(text, context)

@register.simple_tag
def skip_link(target_id, text="Skip to main content"):
    """Template tag for skip links"""
    return accessibility_helper.generate_skip_link(target_id, text)

@register.simple_tag
def sr_only(text):
    """Template tag for screen reader only text"""
    return screen_reader_helper.generate_sr_only_text(text)

@register.simple_tag
def live_region(text, politeness="polite"):
    """Template tag for live regions"""
    return screen_reader_helper.generate_live_region(text, politeness)
