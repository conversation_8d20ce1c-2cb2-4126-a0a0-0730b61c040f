<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}KERNELiOS Admin Panel{% endblock %}</title>
    <link rel="icon" type="image/x-icon" href="{% load static %}{% static 'assets/favicon.ico' %}">
    
    <!-- Base Admin Styles -->
    <style>
        :root {
            --primary: #FFD700;
            --primary-hover: #FFC700;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-muted: #888888;
            --background: #0a0a0a;
            --dark-card: #1a1a1a;
            --border-color: rgba(255, 215, 0, 0.3);
            --success: #22c55e;
            --warning: #fbbf24;
            --error: #ef4444;
            --info: #3b82f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Admin Layout */
        .admin-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }

        /* Sidebar */
        .admin-sidebar {
            background: rgba(26, 26, 26, 0.95);
            border-right: 2px solid var(--border-color);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            width: 280px;
            overflow-y: auto;
        }

        .admin-logo {
            text-align: center;
            margin-bottom: 2rem;
            padding: 0 1rem;
        }

        .admin-logo img {
            height: 50px;
            width: auto;
        }

        .admin-nav {
            list-style: none;
            padding: 0;
        }

        .admin-nav-item {
            margin-bottom: 0.5rem;
        }

        .admin-nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .admin-nav-link:hover,
        .admin-nav-link.active {
            background: rgba(255, 215, 0, 0.1);
            color: var(--primary);
            border-left-color: var(--primary);
        }

        .admin-nav-icon {
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .admin-main {
            margin-left: 280px;
            min-height: 100vh;
            background: var(--background);
        }

        .admin-header {
            background: rgba(26, 26, 26, 0.95);
            border-bottom: 2px solid var(--border-color);
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .admin-header-content h1 {
            color: var(--primary);
            font-size: 1.8rem;
            font-weight: 700;
        }

        .admin-header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .admin-content {
            padding: 2rem;
        }

        /* Cards */
        .admin-card {
            background: rgba(26, 26, 26, 0.95);
            border: 2px solid var(--border-color);
            border-radius: 0.75rem;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .admin-card-header {
            background: rgba(255, 215, 0, 0.1);
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-content {
            padding: 1.5rem;
        }

        /* Grid Layouts */
        .admin-grid {
            display: grid;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .admin-grid-2 { grid-template-columns: repeat(2, 1fr); }
        .admin-grid-3 { grid-template-columns: repeat(3, 1fr); }
        .admin-grid-4 { grid-template-columns: repeat(4, 1fr); }

        /* Forms */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary);
            color: black;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 2px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: var(--primary);
        }

        .btn-success { background: var(--success); }
        .btn-warning { background: var(--warning); }
        .btn-danger { background: var(--error); }
        .btn-info { background: var(--info); }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        /* Messages */
        .messages {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            max-width: 400px;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid;
            backdrop-filter: blur(10px);
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            border-left-color: var(--success);
            color: var(--success);
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-left-color: var(--error);
            color: var(--error);
        }

        .alert-warning {
            background: rgba(251, 191, 36, 0.1);
            border-left-color: var(--warning);
            color: var(--warning);
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border-left-color: var(--info);
            color: var(--info);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .admin-layout {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .admin-sidebar.open {
                transform: translateX(0);
            }
            
            .admin-main {
                margin-left: 0;
            }
            
            .admin-grid-2,
            .admin-grid-3,
            .admin-grid-4 {
                grid-template-columns: 1fr;
            }
        }

        {% block extra_css %}{% endblock %}
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="admin-logo">
                {% load static %}
                <img src="{% static 'assets/logo.png' %}" alt="KERNELiOS">
            </div>
            
            <nav class="admin-nav">
                <ul>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_dashboard' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">📊</span>
                            Dashboard
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_users' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">👥</span>
                            User Management
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_test_versions' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">📝</span>
                            Test Versions
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_instances' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">🎯</span>
                            Exam Instances
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_analytics_dashboard' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">📈</span>
                            Analytics
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_communications' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">💬</span>
                            Communications
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_backup_management' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">💾</span>
                            Backups
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_monitoring_dashboard' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">🔍</span>
                            Monitoring
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_security_audit' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">🔒</span>
                            Security
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_cache_management' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">⚡</span>
                            Cache
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="{% url 'admin_panel:admin_settings' %}" class="admin-nav-link">
                            <span class="admin-nav-icon">⚙️</span>
                            Settings
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Messages -->
    {% if messages %}
        <div class="messages">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    {% block extra_js %}{% endblock %}

    <script>
        // Auto-dismiss messages
        setTimeout(() => {
            const messages = document.querySelectorAll('.alert');
            messages.forEach(msg => {
                msg.style.opacity = '0';
                setTimeout(() => msg.remove(), 300);
            });
        }, 5000);

        // Active nav link highlighting
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.admin-nav-link');
        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    </script>
</body>
</html>
