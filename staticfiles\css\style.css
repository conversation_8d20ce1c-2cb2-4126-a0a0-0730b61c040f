/* KERNELiOS Secure Exam System - Main Styles */

/* CSS Variables for consistent theming */
:root {
    --primary: #FFD700;
    --primary-hover: #FFC700;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    --background: #0a0a0a;
    --dark-card: #1a1a1a;
    --border-color: rgba(255, 215, 0, 0.3);
    --success: #22c55e;
    --warning: #fbbf24;
    --error: #ef4444;
    --info: #3b82f6;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Matrix Rain Background Animation */
.matrix-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
}

.matrix-column {
    position: absolute;
    top: -100%;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: var(--primary);
    animation: matrix-fall linear infinite;
}

@keyframes matrix-fall {
    0% { top: -100%; }
    100% { top: 100%; }
}

/* Header Styles */
.header {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--border-color);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: var(--text-primary);
}

.logo img {
    height: 40px;
    width: auto;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

.nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 120px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

/* Terminal Window Styles */
.terminal-window {
    background: rgba(26, 26, 26, 0.95);
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    position: relative;
}

.terminal-header {
    background: rgba(255, 215, 0, 0.1);
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.terminal-buttons {
    display: flex;
    gap: 0.5rem;
}

.terminal-button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.terminal-button.close { background: #ff5f56; }
.terminal-button.minimize { background: #ffbd2e; }
.terminal-button.maximize { background: #27ca3f; }

.terminal-title {
    color: var(--primary);
    font-weight: 600;
    font-size: 0.9rem;
    margin-left: 1rem;
}

.terminal-content {
    padding: 2rem;
}

/* Form Styles */
.form-container {
    max-width: 400px;
    width: 100%;
}

.form-title {
    color: var(--primary);
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 2rem;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 0.5rem;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary);
    color: black;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    min-height: 44px;
}

.btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--primary);
    color: black;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary);
}

.btn-full {
    width: 100%;
}

/* Alert Messages */
.messages {
    position: fixed;
    top: 100px;
    right: 2rem;
    z-index: 1000;
    max-width: 400px;
}

.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
    backdrop-filter: blur(10px);
    animation: slideIn 0.3s ease;
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border-left-color: var(--success);
    color: var(--success);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: var(--error);
    color: var(--error);
}

.alert-warning {
    background: rgba(251, 191, 36, 0.1);
    border-left-color: var(--warning);
    color: var(--warning);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-left-color: var(--info);
    color: var(--info);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Footer */
.footer {
    background: rgba(26, 26, 26, 0.95);
    border-top: 2px solid var(--border-color);
    padding: 2rem 0;
    text-align: center;
    color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        gap: 1rem;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .terminal-content {
        padding: 1rem;
    }
    
    .form-title {
        font-size: 1.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
*:focus-visible {
    outline: 3px solid var(--primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary: #ffff00;
        --text-primary: #ffffff;
        --background: #000000;
        --dark-card: #000000;
        --border-color: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
