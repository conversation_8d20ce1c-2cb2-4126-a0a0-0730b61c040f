"""
Data Protection and Privacy Utilities
Provides encryption, anonymization, and GDPR compliance tools
"""

import hashlib
import secrets
from cryptography.fernet import Fernet
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from django.utils import timezone
from datetime import timedelta
import json
import os


class DataEncryption:
    """Handle field-level encryption for sensitive data"""
    
    def __init__(self):
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
    
    def _get_or_create_key(self):
        """Get or create encryption key"""
        key_file = os.path.join(settings.BASE_DIR, '.encryption_key')
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def encrypt(self, data):
        """Encrypt sensitive data"""
        if not data:
            return data
        
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        return self.cipher.encrypt(data).decode('utf-8')
    
    def decrypt(self, encrypted_data):
        """Decrypt sensitive data"""
        if not encrypted_data:
            return encrypted_data
        
        try:
            decrypted = self.cipher.decrypt(encrypted_data.encode('utf-8'))
            return decrypted.decode('utf-8')
        except Exception:
            # Return original data if decryption fails (for backward compatibility)
            return encrypted_data


class DataAnonymizer:
    """Anonymize personal data for privacy compliance"""
    
    @staticmethod
    def anonymize_email(email):
        """Anonymize email address"""
        if not email or '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            anonymized_local = '*' * len(local)
        else:
            anonymized_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{anonymized_local}@{domain}"
    
    @staticmethod
    def anonymize_name(name):
        """Anonymize personal name"""
        if not name:
            return name
        
        parts = name.split()
        if len(parts) == 1:
            return parts[0][0] + '*' * (len(parts[0]) - 1) if len(parts[0]) > 1 else '*'
        
        anonymized_parts = []
        for i, part in enumerate(parts):
            if i == 0:  # First name
                anonymized_parts.append(part[0] + '*' * (len(part) - 1) if len(part) > 1 else '*')
            else:  # Last names
                anonymized_parts.append(part[0] + '.' if len(part) > 0 else '*')
        
        return ' '.join(anonymized_parts)
    
    @staticmethod
    def generate_anonymous_id():
        """Generate anonymous identifier"""
        return f"anon_{secrets.token_hex(8)}"


class GDPRCompliance:
    """GDPR compliance utilities"""
    
    @staticmethod
    def create_data_export(user):
        """Create comprehensive data export for a user"""
        from .models import Player, PlayerOnLevel, ExamInstance
        
        try:
            player = Player.objects.get(user=user)
        except Player.DoesNotExist:
            player = None
        
        export_data = {
            'user_info': {
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'date_joined': user.date_joined.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'is_active': user.is_active,
            },
            'exam_data': {},
            'export_timestamp': timezone.now().isoformat(),
            'export_type': 'GDPR_DATA_EXPORT'
        }
        
        if player:
            # Get exam participation data
            attempts = PlayerOnLevel.objects.filter(player=player).select_related('level', 'level__version')
            
            exam_data = {}
            for attempt in attempts:
                version_name = attempt.level.version.name
                if version_name not in exam_data:
                    exam_data[version_name] = {
                        'instance': player.instance.name if player.instance else 'Unknown',
                        'start_time': player.start_time.isoformat() if player.start_time else None,
                        'end_time': player.end_time.isoformat() if player.end_time else None,
                        'score': player.score,
                        'questions': []
                    }
                
                exam_data[version_name]['questions'].append({
                    'question_name': attempt.level.name,
                    'question_order': attempt.level.order,
                    'attempts': attempt.attempts,
                    'correctly_answered': attempt.correctly_answered,
                    'time_spent_seconds': attempt.time_spent_seconds,
                    'start_time': attempt.start_time.isoformat() if attempt.start_time else None,
                    'end_time': attempt.end_time.isoformat() if attempt.end_time else None,
                })
            
            export_data['exam_data'] = exam_data
        
        return export_data
    
    @staticmethod
    def anonymize_user_data(user):
        """Anonymize user data while preserving exam statistics"""
        from .models import Player
        
        # Anonymize user fields
        user.username = DataAnonymizer.generate_anonymous_id()
        user.email = DataAnonymizer.anonymize_email(user.email)
        user.first_name = DataAnonymizer.anonymize_name(user.first_name)
        user.last_name = DataAnonymizer.anonymize_name(user.last_name)
        user.save()
        
        # Mark as anonymized in player record
        try:
            player = Player.objects.get(user=user)
            # Add anonymization flag if it doesn't exist
            if not hasattr(player, 'is_anonymized'):
                # We'll add this field to the model later
                pass
        except Player.DoesNotExist:
            pass
        
        return user
    
    @staticmethod
    def delete_user_data(user):
        """Completely delete user data (right to be forgotten)"""
        from .models import Player, PlayerOnLevel
        
        # Delete all exam attempts
        try:
            player = Player.objects.get(user=user)
            PlayerOnLevel.objects.filter(player=player).delete()
            player.delete()
        except Player.DoesNotExist:
            pass
        
        # Delete user account
        user.delete()
        
        return True


class DataRetention:
    """Data retention and cleanup utilities"""
    
    @staticmethod
    def cleanup_old_data(days_to_keep=365):
        """Clean up old exam data based on retention policy"""
        from .models import PlayerOnLevel, Player
        
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        
        # Find old exam attempts
        old_attempts = PlayerOnLevel.objects.filter(
            start_time__lt=cutoff_date
        )
        
        # Find players with no recent activity
        old_players = Player.objects.filter(
            start_time__lt=cutoff_date,
            end_time__isnull=False
        )
        
        cleanup_stats = {
            'old_attempts_count': old_attempts.count(),
            'old_players_count': old_players.count(),
            'cleanup_date': timezone.now().isoformat()
        }
        
        # Perform cleanup (commented out for safety)
        # old_attempts.delete()
        # old_players.delete()
        
        return cleanup_stats
    
    @staticmethod
    def get_retention_report():
        """Generate data retention report"""
        from .models import Player, PlayerOnLevel, ExamInstance
        
        now = timezone.now()
        
        # Calculate data age statistics
        report = {
            'total_users': Player.objects.count(),
            'total_attempts': PlayerOnLevel.objects.count(),
            'total_instances': ExamInstance.objects.count(),
            'data_age_distribution': {
                'last_30_days': Player.objects.filter(start_time__gte=now - timedelta(days=30)).count(),
                'last_90_days': Player.objects.filter(start_time__gte=now - timedelta(days=90)).count(),
                'last_365_days': Player.objects.filter(start_time__gte=now - timedelta(days=365)).count(),
                'older_than_1_year': Player.objects.filter(start_time__lt=now - timedelta(days=365)).count(),
            },
            'report_generated': now.isoformat()
        }
        
        return report


# Initialize global instances
encryption = DataEncryption()
anonymizer = DataAnonymizer()
gdpr = GDPRCompliance()
retention = DataRetention()
