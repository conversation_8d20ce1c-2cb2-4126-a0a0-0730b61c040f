/**
 * Drag and Drop Question Interface
 * Provides interactive matching and ordering functionality
 */

class DragDropQuestion {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            dragItems: options.dragItems || [],
            dropZones: options.dropZones || [],
            correctMatches: options.correctMatches || [],
            allowMultiple: options.allowMultiple || false,
            showFeedback: options.showFeedback !== false,
            ...options
        };
        
        this.currentMatches = new Map();
        this.isSubmitted = false;
        this.score = 0;
        
        this.init();
    }
    
    init() {
        this.createInterface();
        this.setupEventListeners();
        this.setupDragAndDrop();
    }
    
    createInterface() {
        this.container.innerHTML = `
            <div class="drag-drop-container">
                <div class="drag-drop-header">
                    <h3 class="question-title">🎯 Drag and Drop Question</h3>
                    <div class="drag-drop-actions">
                        <button class="btn-reset" id="resetBtn">
                            <span class="action-icon">🔄</span>
                            Reset
                        </button>
                        <button class="btn-check" id="checkBtn">
                            <span class="action-icon">✅</span>
                            Check Answer
                        </button>
                    </div>
                </div>
                
                <div class="drag-drop-content">
                    <div class="drag-items-section">
                        <h4 class="section-title">📦 Drag Items</h4>
                        <div class="drag-items-container" id="dragItemsContainer">
                            ${this.renderDragItems()}
                        </div>
                    </div>
                    
                    <div class="drop-zones-section">
                        <h4 class="section-title">🎯 Drop Zones</h4>
                        <div class="drop-zones-container" id="dropZonesContainer">
                            ${this.renderDropZones()}
                        </div>
                    </div>
                </div>
                
                <div class="drag-drop-feedback" id="feedbackContainer" style="display: none;">
                    <!-- Feedback will be shown here -->
                </div>
            </div>
        `;
    }
    
    renderDragItems() {
        return this.options.dragItems.map(item => `
            <div class="drag-item" 
                 data-item-id="${item.id}" 
                 data-category="${item.category || ''}"
                 draggable="true">
                <div class="drag-item-content">
                    <span class="drag-handle">⋮⋮</span>
                    <span class="drag-text">${item.text}</span>
                </div>
            </div>
        `).join('');
    }
    
    renderDropZones() {
        return this.options.dropZones.map(zone => `
            <div class="drop-zone" 
                 data-zone-id="${zone.id}"
                 data-accepts="${(zone.accepts || []).join(',')}"
                 data-max-items="${zone.maxItems || (this.options.allowMultiple ? 999 : 1)}">
                <div class="drop-zone-header">
                    <span class="zone-title">${zone.text}</span>
                    <span class="zone-count">0/${zone.maxItems || (this.options.allowMultiple ? '∞' : '1')}</span>
                </div>
                <div class="drop-zone-content" data-zone-id="${zone.id}">
                    <div class="drop-placeholder">Drop items here</div>
                </div>
            </div>
        `).join('');
    }
    
    setupEventListeners() {
        // Reset button
        this.container.querySelector('#resetBtn').addEventListener('click', () => this.reset());
        
        // Check answer button
        this.container.querySelector('#checkBtn').addEventListener('click', () => this.checkAnswer());
    }
    
    setupDragAndDrop() {
        // Setup drag events for drag items
        this.container.querySelectorAll('.drag-item').forEach(item => {
            item.addEventListener('dragstart', (e) => this.handleDragStart(e));
            item.addEventListener('dragend', (e) => this.handleDragEnd(e));
        });
        
        // Setup drop events for drop zones
        this.container.querySelectorAll('.drop-zone-content').forEach(zone => {
            zone.addEventListener('dragover', (e) => this.handleDragOver(e));
            zone.addEventListener('dragenter', (e) => this.handleDragEnter(e));
            zone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            zone.addEventListener('drop', (e) => this.handleDrop(e));
        });
        
        // Setup drag events for items in drop zones (for moving between zones)
        this.setupDroppedItemEvents();
    }
    
    handleDragStart(e) {
        const item = e.target.closest('.drag-item');
        e.dataTransfer.setData('text/plain', item.dataset.itemId);
        e.dataTransfer.setData('application/json', JSON.stringify({
            itemId: item.dataset.itemId,
            category: item.dataset.category,
            text: item.querySelector('.drag-text').textContent
        }));
        
        item.classList.add('dragging');
        
        // Highlight compatible drop zones
        this.highlightCompatibleZones(item.dataset.category);
    }
    
    handleDragEnd(e) {
        const item = e.target.closest('.drag-item');
        item.classList.remove('dragging');
        
        // Remove all highlights
        this.container.querySelectorAll('.drop-zone').forEach(zone => {
            zone.classList.remove('drag-over', 'drag-compatible', 'drag-incompatible');
        });
    }
    
    handleDragOver(e) {
        e.preventDefault();
    }
    
    handleDragEnter(e) {
        e.preventDefault();
        const dropZone = e.target.closest('.drop-zone');
        if (dropZone && this.canAcceptDrop(dropZone, e.dataTransfer)) {
            dropZone.classList.add('drag-over');
        }
    }
    
    handleDragLeave(e) {
        const dropZone = e.target.closest('.drop-zone');
        if (dropZone && !dropZone.contains(e.relatedTarget)) {
            dropZone.classList.remove('drag-over');
        }
    }
    
    handleDrop(e) {
        e.preventDefault();
        
        const dropZone = e.target.closest('.drop-zone');
        const zoneContent = e.target.closest('.drop-zone-content');
        
        if (!dropZone || !zoneContent) return;
        
        try {
            const itemData = JSON.parse(e.dataTransfer.getData('application/json'));
            
            if (this.canAcceptDrop(dropZone, e.dataTransfer)) {
                this.moveItemToZone(itemData, dropZone);
                this.updateZoneCount(dropZone);
                this.updateCurrentMatches();
            }
        } catch (error) {
            console.error('Error handling drop:', error);
        }
        
        dropZone.classList.remove('drag-over');
    }
    
    canAcceptDrop(dropZone, dataTransfer) {
        try {
            const itemData = JSON.parse(dataTransfer.getData('application/json'));
            const acceptedCategories = dropZone.dataset.accepts.split(',').filter(cat => cat.trim());
            const maxItems = parseInt(dropZone.dataset.maxItems) || 1;
            const currentItems = dropZone.querySelectorAll('.dropped-item').length;
            
            // Check category compatibility
            if (acceptedCategories.length > 0 && !acceptedCategories.includes(itemData.category)) {
                return false;
            }
            
            // Check capacity
            if (currentItems >= maxItems) {
                return false;
            }
            
            return true;
        } catch (error) {
            return false;
        }
    }
    
    moveItemToZone(itemData, dropZone) {
        const zoneContent = dropZone.querySelector('.drop-zone-content');
        const placeholder = zoneContent.querySelector('.drop-placeholder');
        
        // Remove item from its current location
        const existingItem = this.container.querySelector(`[data-item-id="${itemData.itemId}"]`);
        if (existingItem) {
            const oldZone = existingItem.closest('.drop-zone');
            existingItem.remove();
            if (oldZone) {
                this.updateZoneCount(oldZone);
            }
        }
        
        // Create dropped item element
        const droppedItem = document.createElement('div');
        droppedItem.className = 'dropped-item drag-item';
        droppedItem.dataset.itemId = itemData.itemId;
        droppedItem.dataset.category = itemData.category;
        droppedItem.draggable = true;
        droppedItem.innerHTML = `
            <div class="drag-item-content">
                <span class="drag-handle">⋮⋮</span>
                <span class="drag-text">${itemData.text}</span>
                <button class="remove-item-btn" onclick="event.stopPropagation(); this.closest('.drag-drop-container').dragDropInstance.removeItem('${itemData.itemId}')">×</button>
            </div>
        `;
        
        // Hide placeholder if this is the first item
        if (placeholder) {
            placeholder.style.display = 'none';
        }
        
        // Add to zone
        zoneContent.appendChild(droppedItem);
        
        // Setup drag events for the dropped item
        this.setupItemDragEvents(droppedItem);
        
        // Add animation
        droppedItem.style.opacity = '0';
        droppedItem.style.transform = 'scale(0.8)';
        setTimeout(() => {
            droppedItem.style.transition = 'all 0.3s ease';
            droppedItem.style.opacity = '1';
            droppedItem.style.transform = 'scale(1)';
        }, 10);
    }
    
    setupItemDragEvents(item) {
        item.addEventListener('dragstart', (e) => this.handleDragStart(e));
        item.addEventListener('dragend', (e) => this.handleDragEnd(e));
    }
    
    setupDroppedItemEvents() {
        // This will be called after items are moved
        this.container.dragDropInstance = this;
    }
    
    removeItem(itemId) {
        const item = this.container.querySelector(`[data-item-id="${itemId}"]`);
        if (item) {
            const zone = item.closest('.drop-zone');
            item.remove();
            
            if (zone) {
                this.updateZoneCount(zone);
                const zoneContent = zone.querySelector('.drop-zone-content');
                if (zoneContent.children.length === 0) {
                    const placeholder = zoneContent.querySelector('.drop-placeholder');
                    if (placeholder) {
                        placeholder.style.display = 'block';
                    }
                }
            }
            
            // Return item to original container
            this.returnItemToOriginal(itemId);
            this.updateCurrentMatches();
        }
    }
    
    returnItemToOriginal(itemId) {
        const originalItem = this.options.dragItems.find(item => item.id === itemId);
        if (originalItem) {
            const dragContainer = this.container.querySelector('#dragItemsContainer');
            const newItem = document.createElement('div');
            newItem.className = 'drag-item';
            newItem.dataset.itemId = originalItem.id;
            newItem.dataset.category = originalItem.category || '';
            newItem.draggable = true;
            newItem.innerHTML = `
                <div class="drag-item-content">
                    <span class="drag-handle">⋮⋮</span>
                    <span class="drag-text">${originalItem.text}</span>
                </div>
            `;
            
            dragContainer.appendChild(newItem);
            this.setupItemDragEvents(newItem);
        }
    }
    
    updateZoneCount(dropZone) {
        const countElement = dropZone.querySelector('.zone-count');
        const currentItems = dropZone.querySelectorAll('.dropped-item').length;
        const maxItems = dropZone.dataset.maxItems;
        
        if (countElement) {
            countElement.textContent = `${currentItems}/${maxItems === '999' ? '∞' : maxItems}`;
        }
    }
    
    highlightCompatibleZones(category) {
        this.container.querySelectorAll('.drop-zone').forEach(zone => {
            const acceptedCategories = zone.dataset.accepts.split(',').filter(cat => cat.trim());
            
            if (acceptedCategories.length === 0 || acceptedCategories.includes(category)) {
                zone.classList.add('drag-compatible');
            } else {
                zone.classList.add('drag-incompatible');
            }
        });
    }
    
    updateCurrentMatches() {
        this.currentMatches.clear();
        
        this.container.querySelectorAll('.drop-zone').forEach(zone => {
            const zoneId = zone.dataset.zoneId;
            const items = zone.querySelectorAll('.dropped-item');
            
            items.forEach(item => {
                this.currentMatches.set(item.dataset.itemId, zoneId);
            });
        });
    }
    
    checkAnswer() {
        if (this.isSubmitted) return;
        
        this.updateCurrentMatches();
        
        let correctMatches = 0;
        const totalMatches = this.options.correctMatches.length;
        const feedback = [];
        
        // Check each correct match
        this.options.correctMatches.forEach(correctMatch => {
            const userMatch = this.currentMatches.get(correctMatch.item);
            const isCorrect = userMatch === correctMatch.zone;
            
            if (isCorrect) {
                correctMatches++;
            }
            
            feedback.push({
                item: correctMatch.item,
                expectedZone: correctMatch.zone,
                userZone: userMatch,
                isCorrect: isCorrect
            });
        });
        
        this.score = totalMatches > 0 ? (correctMatches / totalMatches) * 100 : 0;
        this.isSubmitted = true;
        
        this.showFeedback(feedback);
        
        // Trigger callback if provided
        if (this.options.onSubmit) {
            this.options.onSubmit({
                matches: Object.fromEntries(this.currentMatches),
                score: this.score,
                feedback: feedback
            });
        }
    }
    
    showFeedback(feedback) {
        const feedbackContainer = this.container.querySelector('#feedbackContainer');
        
        const feedbackHtml = `
            <div class="feedback-header">
                <h4>📊 Results</h4>
                <div class="score-display ${this.score >= 70 ? 'passing' : 'failing'}">
                    Score: ${this.score.toFixed(1)}%
                </div>
            </div>
            
            <div class="feedback-details">
                ${feedback.map(item => {
                    const itemData = this.options.dragItems.find(d => d.id === item.item);
                    const expectedZone = this.options.dropZones.find(z => z.id === item.expectedZone);
                    const userZone = this.options.dropZones.find(z => z.id === item.userZone);
                    
                    return `
                        <div class="feedback-item ${item.isCorrect ? 'correct' : 'incorrect'}">
                            <span class="feedback-icon">${item.isCorrect ? '✅' : '❌'}</span>
                            <div class="feedback-content">
                                <strong>"${itemData?.text || item.item}"</strong>
                                <div class="feedback-details-text">
                                    Expected: ${expectedZone?.text || item.expectedZone}
                                    ${item.userZone ? `| Your answer: ${userZone?.text || item.userZone}` : '| Not placed'}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
        
        feedbackContainer.innerHTML = feedbackHtml;
        feedbackContainer.style.display = 'block';
        
        // Disable further interactions
        this.container.querySelectorAll('.drag-item').forEach(item => {
            item.draggable = false;
            item.style.opacity = '0.7';
        });
        
        this.container.querySelector('#checkBtn').disabled = true;
    }
    
    reset() {
        this.currentMatches.clear();
        this.isSubmitted = false;
        this.score = 0;
        
        // Clear all drop zones
        this.container.querySelectorAll('.drop-zone-content').forEach(zoneContent => {
            const placeholder = zoneContent.querySelector('.drop-placeholder');
            zoneContent.innerHTML = '<div class="drop-placeholder">Drop items here</div>';
        });
        
        // Restore all drag items to original container
        const dragContainer = this.container.querySelector('#dragItemsContainer');
        dragContainer.innerHTML = this.renderDragItems();
        
        // Re-setup event listeners
        this.setupDragAndDrop();
        
        // Hide feedback
        this.container.querySelector('#feedbackContainer').style.display = 'none';
        
        // Re-enable check button
        this.container.querySelector('#checkBtn').disabled = false;
        
        // Update zone counts
        this.container.querySelectorAll('.drop-zone').forEach(zone => {
            this.updateZoneCount(zone);
        });
    }
    
    getCurrentAnswer() {
        this.updateCurrentMatches();
        return Object.fromEntries(this.currentMatches);
    }
    
    getScore() {
        return this.score;
    }
}
