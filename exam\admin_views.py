"""
Custom admin views for advanced management functionality
"""

from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_protect
from django.utils import timezone
from django.conf import settings
from django.core.mail import send_mail, get_connection
from django.template.loader import render_to_string
from functools import wraps
import csv
import io
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter
from .models import TestVersion, Level, Player, PlayerOnLevel, ScoringConfig, AppConfig
from .scoring import calculate_total_score, get_score_breakdown

# Custom decorator for admin views
def admin_required_with_message(view_func):
    """Custom admin_required that shows a helpful message and redirects to home"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, 'Please log in to access this page.')
            return redirect('/')
        if not request.user.is_staff:
            messages.error(request, 'Access denied. Admin privileges required.')
            return redirect('/')
        return view_func(request, *args, **kwargs)
    return wrapper


def get_email_connection(app_config):
    """Create email connection using AppConfig settings"""
    if not app_config or not app_config.email_host_user:
        return None

    return get_connection(
        backend=app_config.email_backend,
        host=app_config.email_host,
        port=app_config.email_port,
        username=app_config.email_host_user,
        password=app_config.email_host_password,
        use_tls=app_config.email_use_tls,
        fail_silently=False,
    )


@staff_member_required
def bulk_import_questions(request):
    """Bulk import questions from CSV"""
    if request.method == 'POST':
        csv_file = request.FILES.get('csv_file')
        version_id = request.POST.get('version_id')
        
        if not csv_file or not version_id:
            messages.error(request, 'Please provide both CSV file and test version.')
            return redirect('admin:exam_level_changelist')
        
        try:
            version = TestVersion.objects.get(id=version_id)
            
            # Read CSV file
            file_data = csv_file.read().decode('utf-8')
            csv_reader = csv.DictReader(io.StringIO(file_data))
            
            imported_count = 0
            for row_num, row in enumerate(csv_reader, start=2):
                try:
                    # Create level from CSV row
                    Level.objects.create(
                        name=row['Title'],
                        description=row['Description'],
                        correct_answer=row['Answer'],
                        score=int(row['points']),
                        is_bonus=row['is_bonus'].upper() == 'TRUE',
                        version=version,
                        order=row_num - 1  # Start from 1
                    )
                    imported_count += 1
                except Exception as e:
                    messages.warning(request, f'Error importing row {row_num}: {str(e)}')
            
            messages.success(request, f'Successfully imported {imported_count} questions to {version.name}')
            
        except TestVersion.DoesNotExist:
            messages.error(request, 'Test version not found.')
        except Exception as e:
            messages.error(request, f'Error importing CSV: {str(e)}')
    
    return redirect('admin:exam_level_changelist')


@staff_member_required
def export_results(request):
    """Export exam results to Excel with detailed breakdown"""
    try:
        # Create Excel workbook
        wb = openpyxl.Workbook()

        # Summary sheet
        ws_summary = wb.active
        ws_summary.title = "Summary"

        # Header styling
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

        # Summary headers
        summary_headers = [
            'Username', 'Email', 'Version', 'Score', 'Questions Completed',
            'Total Questions', 'Start Time', 'End Time', 'Total Time (seconds)', 'Status'
        ]

        for col, header in enumerate(summary_headers, 1):
            cell = ws_summary.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center")

        # Write summary data
        players = Player.objects.select_related('user', 'version').all()
        row = 2

        for player in players:
            completed_count = PlayerOnLevel.objects.filter(
                player=player,
                correctly_answered=True
            ).count()

            total_questions = Level.objects.filter(version=player.version).count()
            status = 'Complete' if player.end_time else ('In Progress' if player.start_time else 'Not Started')

            ws_summary.cell(row=row, column=1, value=player.user.username)
            ws_summary.cell(row=row, column=2, value=player.user.email)
            ws_summary.cell(row=row, column=3, value=player.version.name)
            ws_summary.cell(row=row, column=4, value=player.score)
            ws_summary.cell(row=row, column=5, value=completed_count)
            ws_summary.cell(row=row, column=6, value=total_questions)
            ws_summary.cell(row=row, column=7, value=player.start_time.isoformat() if player.start_time else '')
            ws_summary.cell(row=row, column=8, value=player.end_time.isoformat() if player.end_time else '')
            ws_summary.cell(row=row, column=9, value=player.total_time_seconds)
            ws_summary.cell(row=row, column=10, value=status)
            row += 1

        # Auto-adjust column widths
        for col in range(1, len(summary_headers) + 1):
            ws_summary.column_dimensions[get_column_letter(col)].width = 15

        # Detailed breakdown sheet
        ws_detail = wb.create_sheet("Detailed Breakdown")

        detail_headers = [
            'Username', 'Version', 'Question Order', 'Question Name', 'Base Score',
            'Attempts', 'Time Spent (sec)', 'Attempt Penalty', 'Time Penalty',
            'Final Score', 'Correctly Answered'
        ]

        for col, header in enumerate(detail_headers, 1):
            cell = ws_detail.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center")

        # Write detailed data
        row = 2
        for player in players:
            breakdown = get_score_breakdown(player)

            for question in breakdown['questions']:
                ws_detail.cell(row=row, column=1, value=player.user.username)
                ws_detail.cell(row=row, column=2, value=player.version.name)
                ws_detail.cell(row=row, column=3, value=question['order'])
                ws_detail.cell(row=row, column=4, value=question['name'])
                ws_detail.cell(row=row, column=5, value=question['base_score'])
                ws_detail.cell(row=row, column=6, value=question['attempts'])
                ws_detail.cell(row=row, column=7, value=question['time_spent_seconds'])
                ws_detail.cell(row=row, column=8, value=question['attempt_penalty'])
                ws_detail.cell(row=row, column=9, value=question['time_penalty'])
                ws_detail.cell(row=row, column=10, value=question['final_score'])
                ws_detail.cell(row=row, column=11, value='Yes' if question['correctly_answered'] else 'No')
                row += 1

        # Auto-adjust column widths for detail sheet
        for col in range(1, len(detail_headers) + 1):
            ws_detail.column_dimensions[get_column_letter(col)].width = 15

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="exam_results.xlsx"'

        wb.save(response)

        # Update export status
        app_config = AppConfig.objects.first()
        if not app_config:
            app_config = AppConfig.objects.create()

        app_config.export_completed = True
        app_config.last_export_time = timezone.now()
        app_config.save()

        return response

    except Exception as e:
        messages.error(request, f'Error exporting results: {str(e)}')
        return redirect('admin:index')


@staff_member_required
@csrf_protect
def clear_all_data(request):
    """Clear all exam data (only after export)"""
    if request.method == 'POST':
        app_config = AppConfig.objects.first()
        
        if not app_config or not app_config.export_completed:
            return JsonResponse({
                'error': 'Export must be completed before clearing data'
            }, status=403)
        
        try:
            # Clear all exam data
            PlayerOnLevel.objects.all().delete()
            Player.objects.all().delete()
            
            # Reset export status
            app_config.export_completed = False
            app_config.last_export_time = None
            app_config.save()
            
            return JsonResponse({
                'success': True,
                'message': 'All exam data cleared successfully'
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)


@staff_member_required
def recalculate_scores(request):
    """Recalculate all player scores"""
    if request.method == 'POST':
        try:
            updated_count = 0
            
            for player in Player.objects.all():
                old_score = player.score
                new_score = calculate_total_score(player)
                
                if old_score != new_score:
                    player.score = new_score
                    player.save()
                    updated_count += 1
            
            messages.success(request, f'Recalculated scores for {updated_count} players')
            
        except Exception as e:
            messages.error(request, f'Error recalculating scores: {str(e)}')
    
    return redirect('admin:exam_player_changelist')


@admin_required_with_message
def admin_dashboard(request):
    """Custom admin dashboard with statistics"""
    # Get statistics
    total_versions = TestVersion.objects.count()
    total_questions = Level.objects.count()
    total_students = Player.objects.count()
    active_students = Player.objects.filter(start_time__isnull=False, end_time__isnull=True).count()
    completed_students = Player.objects.filter(end_time__isnull=False).count()
    
    # Get app config
    app_config = AppConfig.objects.first()
    if not app_config:
        app_config = AppConfig.objects.create()
    
    # Get scoring config
    scoring_config = ScoringConfig.objects.first()
    if not scoring_config:
        scoring_config = ScoringConfig.objects.create()
    
    context = {
        'total_versions': total_versions,
        'total_questions': total_questions,
        'total_students': total_students,
        'active_students': active_students,
        'completed_students': completed_students,
        'app_config': app_config,
        'scoring_config': scoring_config,
        'versions': TestVersion.objects.all()
    }
    
    return render(request, 'admin/exam_dashboard.html', context)

@staff_member_required
@csrf_protect
def email_results(request):
    """Email exam results to configured address"""
    if request.method == 'POST':
        try:
            app_config = AppConfig.objects.first()
            if not app_config or not app_config.email_host_user:
                return JsonResponse({
                    'error': 'Email configuration not set up'
                }, status=400)

            # Get email address from form
            email_to = request.POST.get('email_to')
            if not email_to:
                return JsonResponse({
                    'error': 'Email address is required'
                }, status=400)

            # Generate summary data
            players = Player.objects.select_related('user', 'version').all()

            summary_data = []
            for player in players:
                completed_count = PlayerOnLevel.objects.filter(
                    player=player,
                    correctly_answered=True
                ).count()

                total_questions = Level.objects.filter(version=player.version).count()
                status = 'Complete' if player.end_time else ('In Progress' if player.start_time else 'Not Started')

                summary_data.append({
                    'username': player.user.username,
                    'email': player.user.email,
                    'version': player.version.name,
                    'score': player.score,
                    'completed_questions': completed_count,
                    'total_questions': total_questions,
                    'status': status,
                    'total_time': player.total_time_seconds
                })

            # Render email template
            email_subject = 'Exam Results Export'
            email_body = render_to_string('admin/email_results.html', {
                'summary_data': summary_data,
                'export_time': timezone.now(),
                'total_students': len(summary_data)
            })

            # Create email connection with AppConfig settings
            connection = get_email_connection(app_config)
            if not connection:
                return JsonResponse({
                    'error': 'Email configuration is incomplete'
                }, status=400)

            # Send email using proper connection
            send_mail(
                subject=email_subject,
                message='',  # Plain text version
                html_message=email_body,
                from_email=app_config.email_host_user,
                recipient_list=[email_to],
                fail_silently=False,
                connection=connection,
            )

            return JsonResponse({
                'success': True,
                'message': f'Results emailed to {email_to}'
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Method not allowed'}, status=405)
