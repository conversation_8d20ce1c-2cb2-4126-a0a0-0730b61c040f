"""
Redis Caching Utilities
Provides caching functionality for performance optimization
"""

from django.core.cache import cache, caches
from django.conf import settings
from django.utils import timezone
from functools import wraps
import json
import hashlib
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """Centralized cache management"""
    
    # Cache prefixes for different data types
    PREFIXES = {
        'user_session': 'user_session',
        'question_data': 'question',
        'scoreboard': 'scoreboard',
        'exam_progress': 'progress',
        'test_version': 'test_version',
        'instance_data': 'instance',
        'analytics': 'analytics',
        'system_stats': 'system_stats'
    }
    
    # Default timeouts for different data types (in seconds)
    TIMEOUTS = {
        'user_session': 1800,      # 30 minutes
        'question_data': 3600,     # 1 hour
        'scoreboard': 300,         # 5 minutes
        'exam_progress': 600,      # 10 minutes
        'test_version': 7200,      # 2 hours
        'instance_data': 1800,     # 30 minutes
        'analytics': 900,          # 15 minutes
        'system_stats': 300        # 5 minutes
    }
    
    def __init__(self, cache_alias='default'):
        self.cache = caches[cache_alias]
    
    def _make_key(self, prefix, identifier):
        """Create a standardized cache key"""
        return f"{prefix}:{identifier}"
    
    def _hash_key(self, data):
        """Create a hash from complex data for use as cache key"""
        if isinstance(data, dict):
            data = json.dumps(data, sort_keys=True)
        elif not isinstance(data, str):
            data = str(data)
        
        return hashlib.md5(data.encode('utf-8')).hexdigest()
    
    def get(self, prefix, identifier, default=None):
        """Get cached data"""
        key = self._make_key(prefix, identifier)
        try:
            return self.cache.get(key, default)
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return default
    
    def set(self, prefix, identifier, data, timeout=None):
        """Set cached data"""
        key = self._make_key(prefix, identifier)
        
        if timeout is None:
            timeout = self.TIMEOUTS.get(prefix, 300)
        
        try:
            return self.cache.set(key, data, timeout)
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, prefix, identifier):
        """Delete cached data"""
        key = self._make_key(prefix, identifier)
        try:
            return self.cache.delete(key)
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    def delete_pattern(self, pattern):
        """Delete all keys matching a pattern"""
        try:
            if hasattr(self.cache, 'delete_pattern'):
                return self.cache.delete_pattern(pattern)
            else:
                # Fallback for cache backends that don't support pattern deletion
                logger.warning("Cache backend doesn't support pattern deletion")
                return False
        except Exception as e:
            logger.error(f"Cache delete pattern error for pattern {pattern}: {e}")
            return False
    
    def clear_prefix(self, prefix):
        """Clear all cache entries with a specific prefix"""
        pattern = f"{prefix}:*"
        return self.delete_pattern(pattern)
    
    def get_stats(self):
        """Get cache statistics"""
        try:
            if hasattr(self.cache, '_cache'):
                # Redis cache
                redis_client = self.cache._cache.get_client()
                info = redis_client.info()
                
                return {
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': info.get('used_memory_human', '0B'),
                    'used_memory_peak': info.get('used_memory_peak_human', '0B'),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0),
                    'uptime_in_seconds': info.get('uptime_in_seconds', 0),
                    'redis_version': info.get('redis_version', 'Unknown')
                }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
        
        return {
            'status': 'Cache statistics not available',
            'backend': str(type(self.cache))
        }
    
    def flush_all(self):
        """Clear all cache data"""
        try:
            self.cache.clear()
            return True
        except Exception as e:
            logger.error(f"Error flushing cache: {e}")
            return False


# Global cache manager instance
cache_manager = CacheManager()


def cache_result(prefix, timeout=None, key_func=None):
    """
    Decorator to cache function results
    
    Args:
        prefix: Cache prefix to use
        timeout: Cache timeout in seconds
        key_func: Function to generate cache key from function args
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation
                key_data = {
                    'func': func.__name__,
                    'args': args,
                    'kwargs': kwargs
                }
                cache_key = cache_manager._hash_key(key_data)
            
            # Try to get from cache
            cached_result = cache_manager.get(prefix, cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.set(prefix, cache_key, result, timeout)
            
            return result
        
        return wrapper
    return decorator


def invalidate_cache(prefix, identifier=None):
    """Invalidate cache entries"""
    if identifier:
        return cache_manager.delete(prefix, identifier)
    else:
        return cache_manager.clear_prefix(prefix)


# Specific caching functions for common use cases

def cache_user_progress(user_id, instance_id, progress_data):
    """Cache user exam progress"""
    key = f"{user_id}_{instance_id}"
    return cache_manager.set('exam_progress', key, progress_data)


def get_cached_user_progress(user_id, instance_id):
    """Get cached user exam progress"""
    key = f"{user_id}_{instance_id}"
    return cache_manager.get('exam_progress', key)


def cache_scoreboard(instance_id, scoreboard_data):
    """Cache scoreboard data"""
    return cache_manager.set('scoreboard', str(instance_id), scoreboard_data)


def get_cached_scoreboard(instance_id):
    """Get cached scoreboard data"""
    return cache_manager.get('scoreboard', str(instance_id))


def invalidate_scoreboard(instance_id):
    """Invalidate scoreboard cache for an instance"""
    return cache_manager.delete('scoreboard', str(instance_id))


def cache_test_version(version_id, version_data):
    """Cache test version data"""
    return cache_manager.set('test_version', str(version_id), version_data)


def get_cached_test_version(version_id):
    """Get cached test version data"""
    return cache_manager.get('test_version', str(version_id))


def cache_system_stats(stats_data):
    """Cache system statistics"""
    timestamp = timezone.now().strftime('%Y%m%d_%H%M')
    return cache_manager.set('system_stats', timestamp, stats_data, timeout=300)


def get_latest_system_stats():
    """Get latest cached system statistics"""
    # This would require a more complex implementation to find the latest timestamp
    # For now, return None and let the system generate fresh stats
    return None


# Cache warming functions

def warm_cache():
    """Warm up the cache with frequently accessed data"""
    try:
        from .models import TestVersion, ExamInstance
        
        # Cache active test versions
        active_versions = TestVersion.objects.filter(is_active=True)
        for version in active_versions:
            version_data = {
                'id': version.id,
                'name': version.name,
                'description': version.description,
                'question_count': version.question_count,
                'max_score': version.max_score
            }
            cache_test_version(version.id, version_data)
        
        # Cache active instances
        active_instances = ExamInstance.objects.filter(is_active=True)
        for instance in active_instances:
            instance_data = {
                'id': instance.id,
                'name': instance.name,
                'version_id': instance.version.id,
                'is_active': instance.is_active,
                'open_for_registration': instance.open_for_registration
            }
            cache_manager.set('instance_data', str(instance.id), instance_data)
        
        logger.info(f"Cache warmed with {active_versions.count()} versions and {active_instances.count()} instances")
        return True
        
    except Exception as e:
        logger.error(f"Error warming cache: {e}")
        return False


# Cache health check

def check_cache_health():
    """Check if cache is working properly"""
    try:
        test_key = 'health_check'
        test_value = {'timestamp': timezone.now().isoformat(), 'status': 'ok'}
        
        # Test set
        cache_manager.set('system_stats', test_key, test_value, timeout=60)
        
        # Test get
        retrieved = cache_manager.get('system_stats', test_key)
        
        if retrieved and retrieved.get('status') == 'ok':
            # Clean up
            cache_manager.delete('system_stats', test_key)
            return True, "Cache is working properly"
        else:
            return False, "Cache read/write test failed"
            
    except Exception as e:
        return False, f"Cache health check failed: {str(e)}"
