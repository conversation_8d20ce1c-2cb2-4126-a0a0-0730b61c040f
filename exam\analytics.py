"""
Advanced Analytics Engine
Provides real-time metrics, performance analytics, and trend analysis
"""

from django.db.models import Count, Avg, Su<PERSON>, <PERSON>, <PERSON>, Q, F
from django.utils import timezone
from datetime import timedelta, datetime
from collections import defaultdict
import json
import math
from .models import (Player, PlayerOnLevel, Level, ExamInstance, TestVersion, 
                     User, Announcement, ChatMessage)
from .cache_utils import cache_manager, cache_result


class AnalyticsEngine:
    """Main analytics engine for generating insights"""
    
    def __init__(self):
        self.cache_timeout = 300  # 5 minutes
    
    @cache_result('analytics', timeout=300, key_func=lambda self, instance_id=None: f"real_time_metrics_{instance_id or 'global'}")
    def get_real_time_metrics(self, instance_id=None):
        """Get real-time exam metrics"""
        
        base_query = Player.objects.all()
        if instance_id:
            base_query = base_query.filter(instance_id=instance_id)
        
        now = timezone.now()
        last_hour = now - timedelta(hours=1)
        last_24h = now - timedelta(hours=24)
        
        metrics = {
            'active_students': base_query.filter(
                start_time__isnull=False,
                end_time__isnull=True
            ).count(),
            
            'completed_exams': base_query.filter(
                end_time__isnull=False
            ).count(),
            
            'students_joined_last_hour': base_query.filter(
                start_time__gte=last_hour
            ).count(),
            
            'students_joined_today': base_query.filter(
                start_time__gte=last_24h
            ).count(),
            
            'average_score': base_query.filter(
                end_time__isnull=False,
                score__isnull=False
            ).aggregate(avg_score=Avg('score'))['avg_score'] or 0,
            
            'total_questions_answered': PlayerOnLevel.objects.filter(
                player__in=base_query,
                attempts__gt=0
            ).count(),
            
            'correct_answers': PlayerOnLevel.objects.filter(
                player__in=base_query,
                correctly_answered=True
            ).count(),
            
            'timestamp': now.isoformat()
        }
        
        # Calculate accuracy rate
        total_attempts = PlayerOnLevel.objects.filter(
            player__in=base_query
        ).aggregate(total=Sum('attempts'))['total'] or 0
        
        if total_attempts > 0:
            metrics['accuracy_rate'] = (metrics['correct_answers'] / total_attempts) * 100
        else:
            metrics['accuracy_rate'] = 0
        
        return metrics
    
    @cache_result('analytics', timeout=600, key_func=lambda self, instance_id=None: f"performance_analytics_{instance_id or 'global'}")
    def get_performance_analytics(self, instance_id=None):
        """Get detailed performance analytics"""
        
        base_query = Player.objects.all()
        if instance_id:
            base_query = base_query.filter(instance_id=instance_id)
        
        # Score distribution
        score_ranges = [
            (0, 20, 'F'),
            (20, 40, 'D'),
            (40, 60, 'C'),
            (60, 80, 'B'),
            (80, 100, 'A')
        ]
        
        score_distribution = {}
        for min_score, max_score, grade in score_ranges:
            count = base_query.filter(
                score__gte=min_score,
                score__lt=max_score,
                end_time__isnull=False
            ).count()
            score_distribution[grade] = count
        
        # Question difficulty analysis
        question_stats = []
        if instance_id:
            instance = ExamInstance.objects.get(id=instance_id)
            questions = Level.objects.filter(version=instance.version)
        else:
            questions = Level.objects.all()
        
        for question in questions:
            attempts = PlayerOnLevel.objects.filter(
                level=question,
                player__in=base_query
            )
            
            total_attempts = attempts.aggregate(total=Sum('attempts'))['total'] or 0
            correct_count = attempts.filter(correctly_answered=True).count()
            avg_time = attempts.filter(
                correctly_answered=True,
                time_spent_seconds__isnull=False
            ).aggregate(avg_time=Avg('time_spent_seconds'))['avg_time'] or 0
            
            difficulty_score = 0
            if total_attempts > 0:
                success_rate = (correct_count / total_attempts) * 100
                difficulty_score = 100 - success_rate
            
            question_stats.append({
                'question_id': question.id,
                'question_name': question.name,
                'total_attempts': total_attempts,
                'correct_answers': correct_count,
                'success_rate': (correct_count / total_attempts * 100) if total_attempts > 0 else 0,
                'difficulty_score': difficulty_score,
                'average_time': round(avg_time, 2),
                'question_type': question.question_type
            })
        
        # Sort by difficulty (hardest first)
        question_stats.sort(key=lambda x: x['difficulty_score'], reverse=True)
        
        # Time-based performance
        time_performance = self._get_time_based_performance(base_query)
        
        return {
            'score_distribution': score_distribution,
            'question_difficulty': question_stats[:10],  # Top 10 hardest questions
            'time_performance': time_performance,
            'total_students': base_query.count(),
            'completed_students': base_query.filter(end_time__isnull=False).count(),
            'average_completion_time': self._get_average_completion_time(base_query)
        }
    
    def _get_time_based_performance(self, base_query):
        """Get performance metrics over time"""
        
        now = timezone.now()
        time_periods = []
        
        # Last 24 hours, grouped by hour
        for i in range(24):
            hour_start = now - timedelta(hours=i+1)
            hour_end = now - timedelta(hours=i)
            
            completed_in_hour = base_query.filter(
                end_time__gte=hour_start,
                end_time__lt=hour_end
            )
            
            avg_score = completed_in_hour.aggregate(
                avg_score=Avg('score')
            )['avg_score'] or 0
            
            time_periods.append({
                'period': hour_start.strftime('%H:00'),
                'completed_exams': completed_in_hour.count(),
                'average_score': round(avg_score, 2)
            })
        
        return list(reversed(time_periods))
    
    def _get_average_completion_time(self, base_query):
        """Calculate average exam completion time"""
        
        completed_players = base_query.filter(
            start_time__isnull=False,
            end_time__isnull=False
        )
        
        total_time = 0
        count = 0
        
        for player in completed_players:
            if player.start_time and player.end_time:
                duration = (player.end_time - player.start_time).total_seconds()
                total_time += duration
                count += 1
        
        if count > 0:
            avg_seconds = total_time / count
            return {
                'seconds': round(avg_seconds, 2),
                'minutes': round(avg_seconds / 60, 2),
                'formatted': self._format_duration(avg_seconds)
            }
        
        return {'seconds': 0, 'minutes': 0, 'formatted': '0m 0s'}
    
    def _format_duration(self, seconds):
        """Format duration in human-readable format"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    
    @cache_result('analytics', timeout=900, key_func=lambda self, days=7: f"trend_analysis_{days}")
    def get_trend_analysis(self, days=7):
        """Get trend analysis over specified period"""
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Daily statistics
        daily_stats = []
        for i in range(days):
            day_start = start_date + timedelta(days=i)
            day_end = day_start + timedelta(days=1)
            
            day_players = Player.objects.filter(
                start_time__gte=day_start,
                start_time__lt=day_end
            )
            
            completed_players = day_players.filter(end_time__isnull=False)
            
            daily_stats.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'new_students': day_players.count(),
                'completed_exams': completed_players.count(),
                'average_score': completed_players.aggregate(
                    avg_score=Avg('score')
                )['avg_score'] or 0,
                'total_questions_answered': PlayerOnLevel.objects.filter(
                    player__in=day_players,
                    attempts__gt=0
                ).count()
            })
        
        # Calculate trends
        if len(daily_stats) >= 2:
            recent_avg = sum(day['new_students'] for day in daily_stats[-3:]) / 3
            earlier_avg = sum(day['new_students'] for day in daily_stats[:3]) / 3
            
            student_trend = ((recent_avg - earlier_avg) / earlier_avg * 100) if earlier_avg > 0 else 0
        else:
            student_trend = 0
        
        return {
            'daily_stats': daily_stats,
            'trends': {
                'student_growth': round(student_trend, 2),
                'total_period_students': sum(day['new_students'] for day in daily_stats),
                'total_period_completions': sum(day['completed_exams'] for day in daily_stats),
                'period_average_score': sum(day['average_score'] for day in daily_stats) / len(daily_stats) if daily_stats else 0
            }
        }
    
    def get_system_health_metrics(self):
        """Get system health and performance metrics"""
        
        now = timezone.now()
        last_hour = now - timedelta(hours=1)
        
        # Database performance indicators
        active_sessions = Player.objects.filter(
            start_time__isnull=False,
            end_time__isnull=True
        ).count()
        
        recent_activity = PlayerOnLevel.objects.filter(
            start_time__gte=last_hour
        ).count()
        
        # Error indicators (you might want to implement error logging)
        system_load = {
            'active_sessions': active_sessions,
            'recent_activity': recent_activity,
            'database_queries_per_minute': recent_activity,  # Simplified metric
            'response_time_estimate': self._estimate_response_time(active_sessions),
            'memory_usage_estimate': active_sessions * 0.1,  # Simplified estimate
            'status': 'healthy' if active_sessions < 100 else 'warning' if active_sessions < 200 else 'critical'
        }
        
        return system_load
    
    def _estimate_response_time(self, active_sessions):
        """Estimate response time based on load"""
        base_time = 50  # Base response time in ms
        load_factor = active_sessions * 2  # Each session adds 2ms
        return base_time + load_factor
    
    def get_user_engagement_metrics(self):
        """Get user engagement and activity metrics"""
        
        now = timezone.now()
        last_week = now - timedelta(days=7)
        last_month = now - timedelta(days=30)
        
        # User activity
        total_users = User.objects.count()
        active_last_week = User.objects.filter(
            last_login__gte=last_week
        ).count()
        active_last_month = User.objects.filter(
            last_login__gte=last_month
        ).count()
        
        # Exam engagement
        total_exam_attempts = Player.objects.count()
        completed_exams = Player.objects.filter(end_time__isnull=False).count()
        completion_rate = (completed_exams / total_exam_attempts * 100) if total_exam_attempts > 0 else 0
        
        # Communication engagement
        total_announcements = Announcement.objects.count()
        recent_announcements = Announcement.objects.filter(
            created_at__gte=last_week
        ).count()
        
        total_chat_messages = ChatMessage.objects.count()
        recent_chat_messages = ChatMessage.objects.filter(
            created_at__gte=last_week
        ).count()
        
        return {
            'user_activity': {
                'total_users': total_users,
                'active_last_week': active_last_week,
                'active_last_month': active_last_month,
                'weekly_activity_rate': (active_last_week / total_users * 100) if total_users > 0 else 0,
                'monthly_activity_rate': (active_last_month / total_users * 100) if total_users > 0 else 0
            },
            'exam_engagement': {
                'total_attempts': total_exam_attempts,
                'completed_exams': completed_exams,
                'completion_rate': completion_rate,
                'average_attempts_per_user': total_exam_attempts / total_users if total_users > 0 else 0
            },
            'communication_engagement': {
                'total_announcements': total_announcements,
                'recent_announcements': recent_announcements,
                'total_chat_messages': total_chat_messages,
                'recent_chat_messages': recent_chat_messages,
                'messages_per_user': total_chat_messages / total_users if total_users > 0 else 0
            }
        }


# Global analytics engine instance
analytics_engine = AnalyticsEngine()
