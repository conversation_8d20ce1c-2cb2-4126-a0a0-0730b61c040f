"""
Automated Backup System
Provides scheduled backups with cloud storage integration and encryption
"""

import os
import json
import gzip
import shutil
import tempfile
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from django.conf import settings
from django.core.management import call_command
from django.utils import timezone
from django.db import connection
from cryptography.fernet import Fernet
import logging

logger = logging.getLogger(__name__)


class BackupManager:
    """Main backup management class"""
    
    def __init__(self):
        self.backup_dir = getattr(settings, 'BACKUP_DIR', os.path.join(settings.BASE_DIR, 'backups'))
        self.encryption_key = self._get_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # Ensure backup directory exists
        Path(self.backup_dir).mkdir(parents=True, exist_ok=True)
    
    def _get_encryption_key(self):
        """Get or create encryption key for backups"""
        key_file = os.path.join(settings.BASE_DIR, '.backup_key')
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def create_full_backup(self, include_media=True, compress=True, encrypt=True):
        """Create a full system backup"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"kernelios_backup_{timestamp}"
        
        try:
            # Create temporary directory for backup
            with tempfile.TemporaryDirectory() as temp_dir:
                backup_path = os.path.join(temp_dir, backup_name)
                os.makedirs(backup_path)
                
                # Backup database
                db_backup_path = self._backup_database(backup_path)
                
                # Backup media files
                if include_media:
                    media_backup_path = self._backup_media_files(backup_path)
                
                # Backup configuration files
                config_backup_path = self._backup_configuration(backup_path)
                
                # Create backup manifest
                manifest = self._create_backup_manifest(
                    backup_name, 
                    timestamp,
                    include_media,
                    compress,
                    encrypt
                )
                
                manifest_path = os.path.join(backup_path, 'manifest.json')
                with open(manifest_path, 'w') as f:
                    json.dump(manifest, f, indent=2)
                
                # Compress if requested
                if compress:
                    backup_path = self._compress_backup(backup_path, temp_dir)
                
                # Encrypt if requested
                if encrypt:
                    backup_path = self._encrypt_backup(backup_path, temp_dir)
                
                # Move to final backup directory
                final_backup_path = os.path.join(self.backup_dir, os.path.basename(backup_path))
                shutil.move(backup_path, final_backup_path)
                
                logger.info(f"Backup created successfully: {final_backup_path}")
                
                return {
                    'success': True,
                    'backup_path': final_backup_path,
                    'backup_name': backup_name,
                    'size': self._get_file_size(final_backup_path),
                    'timestamp': timestamp,
                    'manifest': manifest
                }
                
        except Exception as e:
            logger.error(f"Backup creation failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': timestamp
            }
    
    def _backup_database(self, backup_path):
        """Backup the database"""
        
        db_backup_file = os.path.join(backup_path, 'database.json')
        
        try:
            # Use Django's dumpdata command
            with open(db_backup_file, 'w') as f:
                call_command('dumpdata', 
                           '--natural-foreign', 
                           '--natural-primary',
                           '--indent=2',
                           stdout=f)
            
            logger.info(f"Database backup created: {db_backup_file}")
            return db_backup_file
            
        except Exception as e:
            logger.error(f"Database backup failed: {str(e)}")
            raise
    
    def _backup_media_files(self, backup_path):
        """Backup media files"""
        
        media_backup_dir = os.path.join(backup_path, 'media')
        
        try:
            if os.path.exists(settings.MEDIA_ROOT):
                shutil.copytree(settings.MEDIA_ROOT, media_backup_dir)
                logger.info(f"Media files backup created: {media_backup_dir}")
            else:
                # Create empty media directory
                os.makedirs(media_backup_dir)
                logger.info("No media files to backup")
            
            return media_backup_dir
            
        except Exception as e:
            logger.error(f"Media backup failed: {str(e)}")
            raise
    
    def _backup_configuration(self, backup_path):
        """Backup configuration files"""
        
        config_backup_dir = os.path.join(backup_path, 'config')
        os.makedirs(config_backup_dir)
        
        try:
            # Backup settings file
            settings_file = os.path.join(settings.BASE_DIR, 'secure_exam_system', 'settings.py')
            if os.path.exists(settings_file):
                shutil.copy2(settings_file, config_backup_dir)
            
            # Backup URLs file
            urls_file = os.path.join(settings.BASE_DIR, 'secure_exam_system', 'urls.py')
            if os.path.exists(urls_file):
                shutil.copy2(urls_file, config_backup_dir)
            
            # Backup requirements.txt
            requirements_file = os.path.join(settings.BASE_DIR, 'requirements.txt')
            if os.path.exists(requirements_file):
                shutil.copy2(requirements_file, config_backup_dir)
            
            # Backup environment variables (if .env file exists)
            env_file = os.path.join(settings.BASE_DIR, '.env')
            if os.path.exists(env_file):
                shutil.copy2(env_file, config_backup_dir)
            
            logger.info(f"Configuration backup created: {config_backup_dir}")
            return config_backup_dir
            
        except Exception as e:
            logger.error(f"Configuration backup failed: {str(e)}")
            raise
    
    def _create_backup_manifest(self, backup_name, timestamp, include_media, compress, encrypt):
        """Create backup manifest with metadata"""
        
        from .models import User, TestVersion, ExamInstance, Player
        
        return {
            'backup_name': backup_name,
            'timestamp': timestamp,
            'created_at': timezone.now().isoformat(),
            'version': '1.0',
            'system_info': {
                'django_version': getattr(settings, 'DJANGO_VERSION', 'Unknown'),
                'python_version': f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
                'database_engine': settings.DATABASES['default']['ENGINE'],
            },
            'backup_options': {
                'include_media': include_media,
                'compressed': compress,
                'encrypted': encrypt
            },
            'statistics': {
                'total_users': User.objects.count(),
                'total_test_versions': TestVersion.objects.count(),
                'total_exam_instances': ExamInstance.objects.count(),
                'total_players': Player.objects.count(),
            },
            'files': {
                'database': 'database.json',
                'media': 'media/' if include_media else None,
                'config': 'config/',
                'manifest': 'manifest.json'
            }
        }
    
    def _compress_backup(self, backup_path, temp_dir):
        """Compress backup using gzip"""
        
        compressed_path = f"{backup_path}.tar.gz"
        
        try:
            shutil.make_archive(backup_path, 'gztar', backup_path)
            logger.info(f"Backup compressed: {compressed_path}")
            return compressed_path
            
        except Exception as e:
            logger.error(f"Backup compression failed: {str(e)}")
            raise
    
    def _encrypt_backup(self, backup_path, temp_dir):
        """Encrypt backup file"""
        
        encrypted_path = f"{backup_path}.encrypted"
        
        try:
            with open(backup_path, 'rb') as infile:
                with open(encrypted_path, 'wb') as outfile:
                    # Read and encrypt in chunks
                    while True:
                        chunk = infile.read(8192)
                        if not chunk:
                            break
                        encrypted_chunk = self.cipher.encrypt(chunk)
                        outfile.write(encrypted_chunk)
            
            logger.info(f"Backup encrypted: {encrypted_path}")
            return encrypted_path
            
        except Exception as e:
            logger.error(f"Backup encryption failed: {str(e)}")
            raise
    
    def _get_file_size(self, file_path):
        """Get file size in human-readable format"""
        
        try:
            size_bytes = os.path.getsize(file_path)
            
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size_bytes < 1024.0:
                    return f"{size_bytes:.1f} {unit}"
                size_bytes /= 1024.0
            
            return f"{size_bytes:.1f} TB"
            
        except Exception:
            return "Unknown"
    
    def list_backups(self):
        """List all available backups"""
        
        backups = []
        
        try:
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                
                if os.path.isfile(item_path) and 'kernelios_backup_' in item:
                    backup_info = {
                        'name': item,
                        'path': item_path,
                        'size': self._get_file_size(item_path),
                        'created_at': datetime.fromtimestamp(os.path.getctime(item_path)),
                        'is_encrypted': item.endswith('.encrypted'),
                        'is_compressed': '.tar.gz' in item
                    }
                    backups.append(backup_info)
            
            # Sort by creation date (newest first)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error listing backups: {str(e)}")
        
        return backups
    
    def delete_backup(self, backup_name):
        """Delete a specific backup"""
        
        backup_path = os.path.join(self.backup_dir, backup_name)
        
        try:
            if os.path.exists(backup_path):
                os.remove(backup_path)
                logger.info(f"Backup deleted: {backup_path}")
                return True
            else:
                logger.warning(f"Backup not found: {backup_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting backup: {str(e)}")
            return False
    
    def cleanup_old_backups(self, keep_days=30, keep_count=10):
        """Clean up old backups based on age and count"""
        
        backups = self.list_backups()
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        
        deleted_count = 0
        
        # Delete backups older than keep_days
        for backup in backups:
            if backup['created_at'] < cutoff_date:
                if self.delete_backup(backup['name']):
                    deleted_count += 1
        
        # Keep only the most recent keep_count backups
        remaining_backups = self.list_backups()
        if len(remaining_backups) > keep_count:
            for backup in remaining_backups[keep_count:]:
                if self.delete_backup(backup['name']):
                    deleted_count += 1
        
        logger.info(f"Cleanup completed: {deleted_count} backups deleted")
        return deleted_count
    
    def restore_backup(self, backup_name, restore_media=True):
        """Restore from backup (placeholder - requires careful implementation)"""
        
        # This is a complex operation that should be implemented carefully
        # For now, return a placeholder response
        
        logger.warning("Backup restore functionality not yet implemented")
        return {
            'success': False,
            'error': 'Restore functionality not yet implemented',
            'message': 'Please contact system administrator for backup restoration'
        }


# Global backup manager instance
backup_manager = BackupManager()


def schedule_backup():
    """Function to be called by scheduler for automated backups"""
    
    try:
        result = backup_manager.create_full_backup(
            include_media=True,
            compress=True,
            encrypt=True
        )
        
        if result['success']:
            # Clean up old backups
            backup_manager.cleanup_old_backups(keep_days=30, keep_count=10)
            
            logger.info(f"Scheduled backup completed successfully: {result['backup_name']}")
        else:
            logger.error(f"Scheduled backup failed: {result.get('error', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Scheduled backup error: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }
