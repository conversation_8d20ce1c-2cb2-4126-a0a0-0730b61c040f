<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}KERNELiOS Admin Panel{% endblock %}</title>
    <link rel="icon" type="image/png" href="/static/assets/logo.png">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600;700&family=Orbitron:wght@500;600;700&family=Fira+Code:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* KernelIOS Admin Terminal UI System */
        :root {
            /* Primary Colors - Cyber Gold */
            --primary: #FFD700;
            --primary-light: #FFEB3B;
            --primary-dark: #FFC107;
            --primary-hover: #FFEE58;

            /* Secondary Colors - Deep Purple */
            --secondary: #4B0082;
            --secondary-light: #6A0DAD;
            --secondary-dark: #2E004D;
            --secondary-hover: #5D00A0;

            /* Dark Theme Base */
            --dark: #0A0A0A;
            --dark-lighter: #121212;
            --dark-light: #1A1A1A;
            --dark-card: #161616;
            --dark-terminal: #0D1117;
            --dark-terminal-header: #1C2128;

            /* Gray Scale */
            --gray-750: #3F3F46;
            --gray-850: #27272A;
            --gray-900: #1A1A1A;

            /* Accent Colors */
            --accent: #FF3D00;
            --accent-light: #FF5722;
            --success: #00FF41;
            --warning: #FFA500;
            --error: #FF0040;

            /* Text Colors */
            --text-primary: #FFFFFF;
            --text-secondary: #CCCCCC;
            --text-muted: #999999;
            --text-terminal: #00FF41;
            --text-terminal-secondary: #FFD700;

            /* Terminal Colors */
            --terminal-bg: #0D1117;
            --terminal-border: #30363D;
            --terminal-header: #1C2128;
            --terminal-dot-red: #FF5F56;
            --terminal-dot-yellow: #FFBD2E;
            --terminal-dot-green: #27CA3F;

            /* Shadows and Effects */
            --shadow-terminal: 0 8px 32px rgba(0, 0, 0, 0.6);
            --glow-primary: 0 0 20px rgba(255, 215, 0, 0.3);
            --glow-terminal: 0 0 15px rgba(0, 255, 65, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, var(--dark-lighter) 50%, var(--dark) 100%);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }

        /* Background Animation */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(75, 0, 130, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 61, 0, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Navigation */
        .admin-navbar {
            background: rgba(13, 17, 23, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--terminal-border);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .admin-navbar .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .admin-logo img {
            width: 40px;
            height: 40px;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
        }

        .admin-logo h1 {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            color: var(--primary);
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .admin-nav-links {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .admin-nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-family: 'JetBrains Mono', monospace;
        }

        .admin-nav-link:hover {
            color: var(--primary);
            background: rgba(255, 215, 0, 0.1);
            box-shadow: var(--glow-primary);
        }

        .admin-nav-link.active {
            color: var(--primary);
            background: rgba(255, 215, 0, 0.1);
        }

        /* Main Content */
        .admin-main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            min-height: calc(100vh - 100px);
        }

        /* Terminal Window Styles */
        .terminal-window {
            background: var(--terminal-bg);
            border: 1px solid var(--terminal-border);
            border-radius: 0.75rem;
            box-shadow: var(--shadow-terminal);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .terminal-header {
            background: var(--terminal-header);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--terminal-border);
        }

        .terminal-dots {
            display: flex;
            gap: 0.5rem;
        }

        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .terminal-dot.red { background: var(--terminal-dot-red); }
        .terminal-dot.yellow { background: var(--terminal-dot-yellow); }
        .terminal-dot.green { background: var(--terminal-dot-green); }

        .terminal-title {
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .terminal-content {
            padding: 1.5rem;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-family: 'JetBrains Mono', monospace;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: var(--dark);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--error) 0%, #CC0033 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 0, 64, 0.4);
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
        }

        /* Grid Layouts */
        .admin-grid {
            display: grid;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .admin-grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .admin-grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
        .admin-grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

        /* Cards */
        .admin-card {
            background: var(--dark-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            border-color: rgba(255, 215, 0, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .admin-card h3 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-family: 'Orbitron', sans-serif;
        }

        /* Tables */
        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .admin-table th,
        .admin-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .admin-table th {
            background: rgba(255, 215, 0, 0.1);
            color: var(--primary);
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
        }

        .admin-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .admin-main {
                padding: 1rem;
            }
            
            .admin-navbar .container {
                padding: 0 1rem;
            }
            
            .admin-nav-links {
                gap: 1rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Admin Navigation -->
    <nav class="admin-navbar">
        <div class="container">
            <div class="admin-logo">
                <img src="/static/assets/logo.png" alt="KERNELiOS">
                <h1>Admin Panel</h1>
            </div>
            <div class="admin-nav-links">
                <a href="{% url 'admin_panel:admin_dashboard' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_dashboard' %}active{% endif %}">
                    🏠 Dashboard
                </a>
                <a href="{% url 'admin_panel:admin_test_versions' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_test_versions' %}active{% endif %}">
                    📚 Test Versions
                </a>
                <a href="{% url 'admin_panel:admin_instances' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_instances' %}active{% endif %}">
                    🎯 Instances
                </a>
                <a href="{% url 'admin_panel:admin_users' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_users' %}active{% endif %}">
                    👥 Users
                </a>
                <a href="{% url 'admin_panel:admin_settings' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_settings' %}active{% endif %}">
                    ⚙️ Settings
                </a>
                <a href="{% url 'home' %}" class="admin-nav-link">
                    🔙 Main Site
                </a>
                <span style="color: var(--text-muted); font-family: 'JetBrains Mono', monospace;">
                    admin@{{ user.username }}:~$
                </span>
                <a href="{% url 'logout' %}" class="btn btn-secondary">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="admin-main">
        {% block content %}{% endblock %}
    </main>

    {% block extra_js %}{% endblock %}
</body>
</html>
