from django.urls import path
from . import views

urlpatterns = [
    path('', views.home_view, name='home'),
    path('register/', views.register_view, name='register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('dashboard/', views.dashboard_view, name='dashboard'),
    path('submit/<int:level_id>/', views.submit_answer, name='submit_answer'),
    path('question/<int:level_id>/', views.get_question, name='get_question'),
    path('check-pause-status/', views.check_pause_status, name='check_pause_status'),
    path('scoreboard/', views.scoreboard_view, name='scoreboard'),
    path('score-breakdown/', views.score_breakdown_view, name='score_breakdown'),
    path('teacher/', views.teacher_dashboard, name='teacher_dashboard'),
    path('teacher/create-instance/', views.create_instance, name='create_instance'),
    path('teacher/delete-instance/<int:instance_id>/', views.delete_instance, name='delete_instance'),
    path('teacher/export-instance/<int:instance_id>/', views.export_instance_csv, name='export_instance_csv'),
    path('teacher/instance-questions/<int:instance_id>/', views.instance_questions, name='instance_questions'),
    path('teacher/toggle-registration/<int:instance_id>/', views.toggle_instance_registration, name='toggle_instance_registration'),
    path('teacher/pause-exam/', views.pause_exam, name='pause_exam'),
    path('teacher/reset-password/<int:user_id>/', views.reset_student_password, name='reset_student_password'),
    path('teacher/end-test/', views.end_test, name='end_test'),

    # API endpoints
    path('api/execute-code/', views.execute_code_api, name='execute_code_api'),
]
