{% extends 'admin/base_admin.html' %}

{% block title %}Moodle Integration - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">🎓</span>
            Moodle LMS Integration
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            Connect with Moodle for user sync and grade passback
        </p>
    </div>
    
    <div class="admin-header-actions">
        {% if moodle_configured %}
            <form method="post" style="display: inline;">
                {% csrf_token %}
                <button type="submit" name="action" value="test_connection" class="btn btn-secondary">
                    🔗 Test Connection
                </button>
            </form>
        {% endif %}
    </div>
</div>

<div class="admin-content">
    <!-- Connection Status -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0;">🔗 Connection Status</h3>
        </div>
        
        {% if moodle_configured %}
            {% if connection_status.success %}
                <div class="connection-status success">
                    <div class="status-icon">✅</div>
                    <div class="status-details">
                        <div class="status-title">Connected to Moodle</div>
                        <div class="status-info">
                            <div>Site: {{ connection_status.site_name }}</div>
                            <div>Version: {{ connection_status.moodle_version }}</div>
                            <div>Users: {{ connection_status.user_count }}</div>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="connection-status error">
                    <div class="status-icon">❌</div>
                    <div class="status-details">
                        <div class="status-title">Connection Failed</div>
                        <div class="status-error">{{ connection_status.error }}</div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="connection-status warning">
                <div class="status-icon">⚠️</div>
                <div class="status-details">
                    <div class="status-title">Moodle Not Configured</div>
                    <div class="status-info">
                        Please configure Moodle URL and token in settings to enable integration.
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    {% if connection_status.success %}
        <!-- Course Management -->
        <div class="admin-grid admin-grid-2">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 style="color: var(--text-primary); margin: 0;">📚 Moodle Courses</h3>
                </div>
                
                {% if courses %}
                    <div class="courses-list">
                        {% for course in courses %}
                            <div class="course-item">
                                <div class="course-info">
                                    <div class="course-name">{{ course.name }}</div>
                                    <div class="course-details">
                                        <span>{{ course.shortname }}</span>
                                        <span>{{ course.enrolled_users }} users</span>
                                        <span>{{ course.category }}</span>
                                    </div>
                                </div>
                                
                                <div class="course-actions">
                                    <form method="post" style="display: inline;">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="sync_course_users">
                                        <input type="hidden" name="course_id" value="{{ course.id }}">
                                        <button type="submit" class="btn btn-secondary btn-sm">
                                            👥 Sync Users
                                        </button>
                                    </form>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-text">No courses found</div>
                    </div>
                {% endif %}
            </div>

            <div class="admin-card">
                <div class="admin-card-header">
                    <h3 style="color: var(--text-primary); margin: 0;">📝 Exam Instances</h3>
                </div>
                
                {% if exam_instances %}
                    <div class="instances-list">
                        {% for instance in exam_instances %}
                            <div class="instance-item">
                                <div class="instance-info">
                                    <div class="instance-name">{{ instance.name }}</div>
                                    <div class="instance-details">
                                        <span>{{ instance.version.name }}</span>
                                        <span>{{ instance.players.count }} students</span>
                                    </div>
                                </div>
                                
                                <div class="instance-status">
                                    {% if instance.is_active %}
                                        <span class="status-badge active">Active</span>
                                    {% else %}
                                        <span class="status-badge inactive">Inactive</span>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="empty-text">No exam instances found</div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Grade Passback -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📊 Grade Passback</h3>
            </div>
            
            <form method="post" class="grade-passback-form">
                {% csrf_token %}
                <input type="hidden" name="action" value="send_grades">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="instance_id" class="form-label">Exam Instance</label>
                        <select id="instance_id" name="instance_id" class="form-input" required>
                            <option value="">Select an exam instance...</option>
                            {% for instance in exam_instances %}
                                <option value="{{ instance.id }}">{{ instance.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="course_id" class="form-label">Moodle Course</label>
                        <select id="course_id" name="course_id" class="form-input" required>
                            <option value="">Select a course...</option>
                            {% for course in courses %}
                                <option value="{{ course.id }}">{{ course.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="grade_item_id" class="form-label">Grade Item ID</label>
                        <input type="number" id="grade_item_id" name="grade_item_id" 
                               class="form-input" placeholder="Moodle grade item ID" required>
                        <div class="form-help">
                            Enter the Moodle grade item ID where scores should be sent
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        📤 Send Grades to Moodle
                    </button>
                </div>
            </form>
        </div>
    {% endif %}

    <!-- Configuration Help -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0;">⚙️ Configuration</h3>
        </div>
        
        <div class="config-help">
            <h4>To configure Moodle integration:</h4>
            
            <ol class="config-steps">
                <li>
                    <strong>Enable Web Services in Moodle:</strong>
                    <ul>
                        <li>Go to Site Administration → Advanced Features</li>
                        <li>Enable "Enable web services"</li>
                    </ul>
                </li>
                
                <li>
                    <strong>Create a Web Service Token:</strong>
                    <ul>
                        <li>Go to Site Administration → Server → Web Services → Manage tokens</li>
                        <li>Create a new token for the KERNELiOS integration</li>
                    </ul>
                </li>
                
                <li>
                    <strong>Configure KERNELiOS Settings:</strong>
                    <ul>
                        <li>Add MOODLE_URL to your settings</li>
                        <li>Add MOODLE_TOKEN to your settings</li>
                    </ul>
                </li>
            </ol>
            
            <div class="config-example">
                <h5>Example Settings:</h5>
                <pre><code># In your settings.py or environment variables
MOODLE_URL = 'https://your-moodle-site.com'
MOODLE_TOKEN = 'your-web-service-token'</code></pre>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.connection-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 2px solid;
}

.connection-status.success {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
}

.connection-status.error {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
}

.connection-status.warning {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
}

.status-icon {
    font-size: 2rem;
}

.status-details {
    flex: 1;
}

.status-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.status-info {
    color: var(--text-muted);
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.status-error {
    color: #ef4444;
    font-weight: 500;
}

.courses-list,
.instances-list {
    display: grid;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.course-item,
.instance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.course-info,
.instance-info {
    flex: 1;
}

.course-name,
.instance-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.course-details,
.instance-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-badge.inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.grade-passback-form {
    display: grid;
    gap: 2rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-help {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
}

.config-help {
    color: var(--text-primary);
}

.config-help h4 {
    color: var(--primary);
    margin-bottom: 1rem;
}

.config-steps {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.config-steps li {
    margin-bottom: 1rem;
}

.config-steps ul {
    margin-top: 0.5rem;
    padding-left: 1rem;
}

.config-example {
    margin-top: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.config-example h5 {
    color: var(--primary);
    margin-bottom: 1rem;
}

.config-example pre {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem;
    border-radius: 0.25rem;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
}

.empty-text {
    font-style: italic;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Auto-populate grade item ID based on course selection
document.getElementById('course_id').addEventListener('change', function() {
    const courseId = this.value;
    if (courseId) {
        // In a real implementation, you might fetch grade items for the selected course
        console.log('Selected course:', courseId);
    }
});

// Validate form before submission
document.querySelector('.grade-passback-form').addEventListener('submit', function(e) {
    const instanceId = document.getElementById('instance_id').value;
    const courseId = document.getElementById('course_id').value;
    const gradeItemId = document.getElementById('grade_item_id').value;
    
    if (!instanceId || !courseId || !gradeItemId) {
        e.preventDefault();
        alert('Please fill in all required fields');
        return;
    }
    
    if (!confirm('Are you sure you want to send grades to Moodle? This action cannot be undone.')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
