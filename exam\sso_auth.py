"""
SSO Authentication System
Provides SAML and OAuth integration for enterprise authentication
"""

import json
import base64
import hashlib
import hmac
import urllib.parse
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth import login, authenticate
from django.contrib.auth.models import User
from django.http import HttpResponseRedirect, JsonResponse
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import logging

logger = logging.getLogger(__name__)


class SSOProvider:
    """Base SSO provider class"""
    
    def __init__(self, provider_config):
        self.config = provider_config
        self.provider_name = provider_config.get('name', 'Unknown')
        self.enabled = provider_config.get('enabled', False)
    
    def get_auth_url(self, request):
        """Get authentication URL for this provider"""
        raise NotImplementedError
    
    def handle_callback(self, request):
        """Handle authentication callback"""
        raise NotImplementedError
    
    def get_user_info(self, token_data):
        """Extract user information from token"""
        raise NotImplementedError


class OAuthProvider(SSOProvider):
    """OAuth 2.0 provider implementation"""
    
    def __init__(self, provider_config):
        super().__init__(provider_config)
        self.client_id = provider_config.get('client_id', '')
        self.client_secret = provider_config.get('client_secret', '')
        self.auth_url = provider_config.get('auth_url', '')
        self.token_url = provider_config.get('token_url', '')
        self.user_info_url = provider_config.get('user_info_url', '')
        self.scope = provider_config.get('scope', 'openid email profile')
    
    def get_auth_url(self, request):
        """Generate OAuth authorization URL"""
        
        if not self.enabled or not self.client_id:
            return None
        
        # Generate state parameter for security
        state = self._generate_state(request)
        request.session[f'oauth_state_{self.provider_name}'] = state
        
        # Build redirect URI
        redirect_uri = request.build_absolute_uri(
            reverse('sso_callback', kwargs={'provider': self.provider_name})
        )
        
        params = {
            'client_id': self.client_id,
            'response_type': 'code',
            'scope': self.scope,
            'redirect_uri': redirect_uri,
            'state': state
        }
        
        return f"{self.auth_url}?{urllib.parse.urlencode(params)}"
    
    def handle_callback(self, request):
        """Handle OAuth callback"""
        
        code = request.GET.get('code')
        state = request.GET.get('state')
        error = request.GET.get('error')
        
        if error:
            logger.error(f"OAuth error from {self.provider_name}: {error}")
            return {'error': f'Authentication failed: {error}'}
        
        if not code:
            return {'error': 'Authorization code not received'}
        
        # Verify state parameter
        expected_state = request.session.get(f'oauth_state_{self.provider_name}')
        if not state or state != expected_state:
            return {'error': 'Invalid state parameter'}
        
        # Exchange code for token
        token_data = self._exchange_code_for_token(code, request)
        if 'error' in token_data:
            return token_data
        
        # Get user information
        user_info = self.get_user_info(token_data)
        if 'error' in user_info:
            return user_info
        
        # Create or update user
        user_result = self._create_or_update_user(user_info)
        
        return user_result
    
    def _generate_state(self, request):
        """Generate secure state parameter"""
        import secrets
        return secrets.token_urlsafe(32)
    
    def _exchange_code_for_token(self, code, request):
        """Exchange authorization code for access token"""
        
        redirect_uri = request.build_absolute_uri(
            reverse('sso_callback', kwargs={'provider': self.provider_name})
        )
        
        data = {
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': redirect_uri,
            'client_id': self.client_id,
            'client_secret': self.client_secret
        }
        
        try:
            import requests
            response = requests.post(self.token_url, data=data, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Token exchange failed for {self.provider_name}: {str(e)}")
            return {'error': 'Token exchange failed'}
    
    def get_user_info(self, token_data):
        """Get user information using access token"""
        
        access_token = token_data.get('access_token')
        if not access_token:
            return {'error': 'No access token received'}
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Accept': 'application/json'
        }
        
        try:
            import requests
            response = requests.get(self.user_info_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            user_data = response.json()
            
            # Normalize user data
            return {
                'email': user_data.get('email', ''),
                'first_name': user_data.get('given_name', user_data.get('first_name', '')),
                'last_name': user_data.get('family_name', user_data.get('last_name', '')),
                'username': user_data.get('preferred_username', user_data.get('email', '')),
                'provider': self.provider_name,
                'provider_id': user_data.get('sub', user_data.get('id', ''))
            }
            
        except Exception as e:
            logger.error(f"User info request failed for {self.provider_name}: {str(e)}")
            return {'error': 'Failed to get user information'}
    
    def _create_or_update_user(self, user_info):
        """Create or update Django user from SSO data"""
        
        email = user_info.get('email', '')
        username = user_info.get('username', email)
        
        if not email:
            return {'error': 'Email address is required'}
        
        try:
            # Try to find existing user by email
            user = User.objects.filter(email=email).first()
            
            if user:
                # Update existing user
                user.first_name = user_info.get('first_name', '')
                user.last_name = user_info.get('last_name', '')
                user.is_active = True
                user.save()
                
                logger.info(f"Updated existing user {user.username} via {self.provider_name}")
                created = False
            else:
                # Create new user
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    first_name=user_info.get('first_name', ''),
                    last_name=user_info.get('last_name', ''),
                    is_active=True
                )
                
                logger.info(f"Created new user {user.username} via {self.provider_name}")
                created = True
            
            return {
                'success': True,
                'user': user,
                'created': created
            }
            
        except Exception as e:
            logger.error(f"User creation/update failed: {str(e)}")
            return {'error': 'Failed to create or update user'}


class SAMLProvider(SSOProvider):
    """SAML 2.0 provider implementation (simplified)"""
    
    def __init__(self, provider_config):
        super().__init__(provider_config)
        self.entity_id = provider_config.get('entity_id', '')
        self.sso_url = provider_config.get('sso_url', '')
        self.x509_cert = provider_config.get('x509_cert', '')
        self.attribute_mapping = provider_config.get('attribute_mapping', {})
    
    def get_auth_url(self, request):
        """Generate SAML authentication URL"""
        
        if not self.enabled or not self.sso_url:
            return None
        
        # This is a simplified implementation
        # In production, use a proper SAML library like python3-saml
        
        # Generate SAML request (simplified)
        saml_request = self._generate_saml_request(request)
        encoded_request = base64.b64encode(saml_request.encode()).decode()
        
        params = {
            'SAMLRequest': encoded_request,
            'RelayState': request.build_absolute_uri(reverse('home'))
        }
        
        return f"{self.sso_url}?{urllib.parse.urlencode(params)}"
    
    def handle_callback(self, request):
        """Handle SAML response"""
        
        saml_response = request.POST.get('SAMLResponse')
        if not saml_response:
            return {'error': 'No SAML response received'}
        
        # Decode and validate SAML response (simplified)
        try:
            decoded_response = base64.b64decode(saml_response).decode()
            user_info = self._parse_saml_response(decoded_response)
            
            if 'error' in user_info:
                return user_info
            
            # Create or update user
            user_result = self._create_or_update_user(user_info)
            return user_result
            
        except Exception as e:
            logger.error(f"SAML response processing failed: {str(e)}")
            return {'error': 'Failed to process SAML response'}
    
    def _generate_saml_request(self, request):
        """Generate SAML authentication request (simplified)"""
        
        # This is a very simplified SAML request
        # In production, use a proper SAML library
        
        return f"""<?xml version="1.0" encoding="UTF-8"?>
<samlp:AuthnRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                    ID="_{hashlib.md5(str(timezone.now()).encode()).hexdigest()}"
                    Version="2.0"
                    IssueInstant="{timezone.now().isoformat()}"
                    Destination="{self.sso_url}">
    <saml:Issuer xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion">{self.entity_id}</saml:Issuer>
</samlp:AuthnRequest>"""
    
    def _parse_saml_response(self, response_xml):
        """Parse SAML response and extract user data (simplified)"""
        
        # This is a very simplified parser
        # In production, use a proper SAML library with signature validation
        
        try:
            # Extract basic user information (simplified)
            # This would need proper XML parsing and validation
            
            return {
                'email': '<EMAIL>',  # Extract from SAML attributes
                'first_name': 'John',         # Extract from SAML attributes
                'last_name': 'Doe',           # Extract from SAML attributes
                'username': 'john.doe',       # Extract from SAML attributes
                'provider': self.provider_name,
                'provider_id': 'saml_user_id'
            }
            
        except Exception as e:
            logger.error(f"SAML response parsing failed: {str(e)}")
            return {'error': 'Failed to parse SAML response'}
    
    def _create_or_update_user(self, user_info):
        """Create or update Django user from SAML data"""
        
        # Same implementation as OAuth
        email = user_info.get('email', '')
        username = user_info.get('username', email)
        
        if not email:
            return {'error': 'Email address is required'}
        
        try:
            user = User.objects.filter(email=email).first()
            
            if user:
                user.first_name = user_info.get('first_name', '')
                user.last_name = user_info.get('last_name', '')
                user.is_active = True
                user.save()
                created = False
            else:
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    first_name=user_info.get('first_name', ''),
                    last_name=user_info.get('last_name', ''),
                    is_active=True
                )
                created = True
            
            return {
                'success': True,
                'user': user,
                'created': created
            }
            
        except Exception as e:
            logger.error(f"User creation/update failed: {str(e)}")
            return {'error': 'Failed to create or update user'}


class SSOManager:
    """Main SSO management class"""
    
    def __init__(self):
        self.providers = {}
        self._load_providers()
    
    def _load_providers(self):
        """Load SSO providers from settings"""
        
        sso_config = getattr(settings, 'SSO_PROVIDERS', {})
        
        for provider_name, config in sso_config.items():
            provider_type = config.get('type', 'oauth')
            
            if provider_type == 'oauth':
                self.providers[provider_name] = OAuthProvider(config)
            elif provider_type == 'saml':
                self.providers[provider_name] = SAMLProvider(config)
            else:
                logger.warning(f"Unknown SSO provider type: {provider_type}")
    
    def get_provider(self, provider_name):
        """Get SSO provider by name"""
        return self.providers.get(provider_name)
    
    def get_enabled_providers(self):
        """Get list of enabled SSO providers"""
        return {name: provider for name, provider in self.providers.items() 
                if provider.enabled}
    
    def get_auth_urls(self, request):
        """Get authentication URLs for all enabled providers"""
        
        auth_urls = {}
        for name, provider in self.get_enabled_providers().items():
            url = provider.get_auth_url(request)
            if url:
                auth_urls[name] = {
                    'url': url,
                    'display_name': provider.config.get('display_name', name.title()),
                    'icon': provider.config.get('icon', '🔐')
                }
        
        return auth_urls


# Global SSO manager instance
sso_manager = SSOManager()


# Example SSO configuration for settings.py
SSO_PROVIDERS_EXAMPLE = {
    'google': {
        'type': 'oauth',
        'name': 'google',
        'display_name': 'Google',
        'icon': '🔍',
        'enabled': False,  # Set to True to enable
        'client_id': 'your-google-client-id',
        'client_secret': 'your-google-client-secret',
        'auth_url': 'https://accounts.google.com/o/oauth2/v2/auth',
        'token_url': 'https://oauth2.googleapis.com/token',
        'user_info_url': 'https://www.googleapis.com/oauth2/v2/userinfo',
        'scope': 'openid email profile'
    },
    'microsoft': {
        'type': 'oauth',
        'name': 'microsoft',
        'display_name': 'Microsoft',
        'icon': '🏢',
        'enabled': False,  # Set to True to enable
        'client_id': 'your-microsoft-client-id',
        'client_secret': 'your-microsoft-client-secret',
        'auth_url': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        'token_url': 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
        'user_info_url': 'https://graph.microsoft.com/v1.0/me',
        'scope': 'openid email profile'
    },
    'saml_provider': {
        'type': 'saml',
        'name': 'saml_provider',
        'display_name': 'Enterprise SSO',
        'icon': '🏛️',
        'enabled': False,  # Set to True to enable
        'entity_id': 'your-entity-id',
        'sso_url': 'https://your-idp.com/sso',
        'x509_cert': 'your-x509-certificate',
        'attribute_mapping': {
            'email': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
            'first_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
            'last_name': 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname'
        }
    }
}
