{% extends 'admin/base_admin.html' %}

{% block title %}Cache Management - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">🚀</span>
            Cache Management
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            Monitor and manage Redis cache performance
        </p>
    </div>
</div>

<div class="admin-content">
    <!-- Cache Health Status -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                <span style="font-size: 1.5rem;">💓</span>
                Cache Health Status
            </h3>
            <div class="cache-health-indicator {{ cache_health|yesno:'healthy,unhealthy' }}">
                <span class="health-icon">{{ cache_health|yesno:'✅,❌' }}</span>
                <span class="health-text">{{ health_message }}</span>
            </div>
        </div>
    </div>

    <!-- Cache Statistics -->
    <div class="admin-grid admin-grid-2">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-size: 1.5rem;">📊</span>
                    Cache Statistics
                </h3>
                <button onclick="refreshStats()" class="btn btn-secondary btn-sm">
                    🔄 Refresh
                </button>
            </div>
            
            <div class="cache-stats-grid" id="cacheStats">
                {% if cache_stats.redis_version %}
                    <div class="stat-item">
                        <div class="stat-label">Redis Version</div>
                        <div class="stat-value">{{ cache_stats.redis_version }}</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">Connected Clients</div>
                        <div class="stat-value">{{ cache_stats.connected_clients }}</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">Memory Used</div>
                        <div class="stat-value">{{ cache_stats.used_memory }}</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">Memory Peak</div>
                        <div class="stat-value">{{ cache_stats.used_memory_peak }}</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">Cache Hits</div>
                        <div class="stat-value">{{ cache_stats.keyspace_hits|default:"0" }}</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">Cache Misses</div>
                        <div class="stat-value">{{ cache_stats.keyspace_misses|default:"0" }}</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">Hit Ratio</div>
                        <div class="stat-value">
                            {% if cache_stats.keyspace_hits and cache_stats.keyspace_misses %}
                                {% widthratio cache_stats.keyspace_hits cache_stats.keyspace_hits|add:cache_stats.keyspace_misses 100 %}%
                            {% else %}
                                N/A
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">Uptime</div>
                        <div class="stat-value">
                            {% if cache_stats.uptime_in_seconds %}
                                {{ cache_stats.uptime_in_seconds|floatformat:0 }}s
                            {% else %}
                                N/A
                            {% endif %}
                        </div>
                    </div>
                {% else %}
                    <div class="stat-item full-width">
                        <div class="stat-label">Status</div>
                        <div class="stat-value">{{ cache_stats.status|default:"Cache not available" }}</div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Cache Actions -->
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-size: 1.5rem;">⚡</span>
                    Cache Actions
                </h3>
            </div>
            
            <div class="cache-actions">
                <button onclick="warmCache()" class="btn btn-primary cache-action-btn">
                    <span class="btn-icon">🔥</span>
                    Warm Cache
                </button>
                
                <button onclick="clearAllCache()" class="btn btn-warning cache-action-btn">
                    <span class="btn-icon">🧹</span>
                    Clear All Cache
                </button>
                
                <div class="cache-prefix-section">
                    <label class="form-label">Clear Specific Prefix:</label>
                    <div style="display: flex; gap: 0.5rem;">
                        <select id="prefixSelect" class="form-input" style="flex: 1;">
                            <option value="">Select prefix...</option>
                            {% for prefix in cache_prefixes %}
                                <option value="{{ prefix }}">{{ prefix }}</option>
                            {% endfor %}
                        </select>
                        <button onclick="clearPrefix()" class="btn btn-secondary">
                            Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Monitoring -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                <span style="font-size: 1.5rem;">📈</span>
                Real-time Monitoring
            </h3>
            <div class="monitoring-controls">
                <button onclick="toggleMonitoring()" id="monitoringBtn" class="btn btn-secondary btn-sm">
                    ▶️ Start Monitoring
                </button>
                <span class="monitoring-interval">
                    Update every <span id="intervalDisplay">5</span>s
                </span>
            </div>
        </div>
        
        <div class="monitoring-display" id="monitoringDisplay">
            <div class="monitoring-placeholder">
                <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
                <p>Click "Start Monitoring" to see real-time cache statistics</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.cache-health-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 600;
}

.cache-health-indicator.healthy {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.cache-health-indicator.unhealthy {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.cache-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
}

.stat-item.full-width {
    grid-column: 1 / -1;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    color: var(--primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.cache-actions {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.cache-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    font-size: 1rem;
}

.cache-prefix-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
}

.monitoring-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.monitoring-interval {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.monitoring-display {
    margin-top: 1rem;
    min-height: 200px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0.5rem;
    padding: 2rem;
}

.monitoring-placeholder {
    text-align: center;
    color: var(--text-muted);
}

.monitoring-active {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.monitoring-metric {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
}

.metric-label {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.metric-value {
    color: var(--primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let monitoringInterval = null;
let isMonitoring = false;

async function refreshStats() {
    try {
        const response = await fetch('{% url "admin_panel:admin_cache_api" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ action: 'get_stats' })
        });
        
        const result = await response.json();
        
        if (result.success) {
            updateStatsDisplay(result.stats);
            updateHealthIndicator(result.health, result.health_message);
        } else {
            showMessage('Failed to refresh stats', 'error');
        }
    } catch (error) {
        showMessage('Error refreshing stats: ' + error.message, 'error');
    }
}

function updateStatsDisplay(stats) {
    // Update the stats display with new data
    // This would be implemented based on the stats structure
    console.log('Updated stats:', stats);
}

function updateHealthIndicator(health, message) {
    const indicator = document.querySelector('.cache-health-indicator');
    const icon = indicator.querySelector('.health-icon');
    const text = indicator.querySelector('.health-text');
    
    indicator.className = `cache-health-indicator ${health ? 'healthy' : 'unhealthy'}`;
    icon.textContent = health ? '✅' : '❌';
    text.textContent = message;
}

async function warmCache() {
    if (!confirm('Warm the cache with frequently accessed data?')) return;
    
    try {
        const response = await fetch('{% url "admin_panel:admin_cache_api" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ action: 'warm_cache' })
        });
        
        const result = await response.json();
        showMessage(result.message, result.success ? 'success' : 'error');
        
        if (result.success) {
            refreshStats();
        }
    } catch (error) {
        showMessage('Error warming cache: ' + error.message, 'error');
    }
}

async function clearAllCache() {
    if (!confirm('Clear ALL cache data? This action cannot be undone.')) return;
    
    try {
        const response = await fetch('{% url "admin_panel:admin_cache_api" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ action: 'clear_all' })
        });
        
        const result = await response.json();
        showMessage(result.message, result.success ? 'success' : 'error');
        
        if (result.success) {
            refreshStats();
        }
    } catch (error) {
        showMessage('Error clearing cache: ' + error.message, 'error');
    }
}

async function clearPrefix() {
    const prefix = document.getElementById('prefixSelect').value;
    if (!prefix) {
        showMessage('Please select a prefix to clear', 'error');
        return;
    }
    
    if (!confirm(`Clear all cache entries with prefix "${prefix}"?`)) return;
    
    try {
        const response = await fetch('{% url "admin_panel:admin_cache_api" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ action: 'clear_prefix', prefix: prefix })
        });
        
        const result = await response.json();
        showMessage(result.message, result.success ? 'success' : 'error');
        
        if (result.success) {
            refreshStats();
        }
    } catch (error) {
        showMessage('Error clearing prefix: ' + error.message, 'error');
    }
}

function toggleMonitoring() {
    const btn = document.getElementById('monitoringBtn');
    
    if (isMonitoring) {
        clearInterval(monitoringInterval);
        isMonitoring = false;
        btn.innerHTML = '▶️ Start Monitoring';
        
        document.getElementById('monitoringDisplay').innerHTML = `
            <div class="monitoring-placeholder">
                <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
                <p>Click "Start Monitoring" to see real-time cache statistics</p>
            </div>
        `;
    } else {
        isMonitoring = true;
        btn.innerHTML = '⏸️ Stop Monitoring';
        
        // Start monitoring
        monitoringInterval = setInterval(refreshStats, 5000);
        refreshStats(); // Initial load
    }
}

function showMessage(message, type) {
    // Create a temporary message element
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        color: white;
        font-weight: bold;
        z-index: 9999;
        background: ${type === 'success' ? 'var(--success)' : 'var(--error)'};
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // Remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

// Auto-refresh stats every 30 seconds
setInterval(refreshStats, 30000);
</script>
{% endblock %}
