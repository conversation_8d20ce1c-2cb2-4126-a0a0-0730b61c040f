from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.db.models.signals import post_delete
from django.dispatch import receiver
from datetime import timedelta

# Create your models here.

class TestVersion(models.Model):
    name = models.CharField(max_length=50, unique=True)  # V1, V2, etc.
    description = models.TextField(blank=True)
    max_score = models.IntegerField(default=100)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    @property
    def question_count(self):
        """Total number of questions in this version"""
        return self.levels.count()

    @property
    def normal_question_count(self):
        """Number of normal (non-bonus) questions"""
        return self.levels.filter(is_bonus=False).count()

    @property
    def bonus_question_count(self):
        """Number of bonus questions"""
        return self.levels.filter(is_bonus=True).count()

    def calculate_automatic_scores(self, target_total=100):
        """
        Intelligent scoring system that automatically distributes points.

        Rules:
        1. Normal questions always total exactly 100 points
        2. Bonus questions provide extra credit (same value as normal questions)
        3. Points are distributed as evenly as possible
        4. Remainder points are distributed to first questions to ensure exact total

        Args:
            target_total (int): Target total for normal questions (default: 100)
        """
        normal_questions = self.levels.filter(is_bonus=False).order_by('order')
        bonus_questions = self.levels.filter(is_bonus=True).order_by('order')

        normal_count = normal_questions.count()
        bonus_count = bonus_questions.count()

        if normal_count == 0:
            # If no normal questions, set all bonus questions to 0 and max_score to 0
            for question in bonus_questions:
                question.score = 0
                question.save(update_fields=['score'])
            self.max_score = 0
            self.save(update_fields=['max_score'])
            return

        # Calculate base points per normal question
        base_points = target_total // normal_count
        remainder = target_total % normal_count

        # Distribute points to normal questions
        for i, question in enumerate(normal_questions):
            # First 'remainder' questions get an extra point
            question.score = base_points + (1 if i < remainder else 0)
            question.save(update_fields=['score'])

        # Bonus questions get the same average value as normal questions
        if bonus_count > 0:
            avg_normal_points = target_total // normal_count
            for question in bonus_questions:
                question.score = avg_normal_points
                question.save(update_fields=['score'])

        # Update the version's max_score
        self.max_score = target_total
        self.save(update_fields=['max_score'])

    def get_score_distribution(self):
        """Get detailed score distribution information"""
        normal_questions = self.levels.filter(is_bonus=False)
        bonus_questions = self.levels.filter(is_bonus=True)

        normal_total = sum(q.score for q in normal_questions)
        bonus_total = sum(q.score for q in bonus_questions)

        # Calculate score ranges
        normal_scores = [q.score for q in normal_questions]
        bonus_scores = [q.score for q in bonus_questions]

        return {
            'normal_count': normal_questions.count(),
            'bonus_count': bonus_questions.count(),
            'normal_total': normal_total,
            'bonus_total': bonus_total,
            'max_possible': normal_total + bonus_total,
            'base_total': 100,  # Target for normal questions
            'is_balanced': normal_total == 100,  # Check if properly balanced
            'normal_min': min(normal_scores) if normal_scores else 0,
            'normal_max': max(normal_scores) if normal_scores else 0,
            'normal_avg': round(normal_total / len(normal_scores), 1) if normal_scores else 0,
            'bonus_min': min(bonus_scores) if bonus_scores else 0,
            'bonus_max': max(bonus_scores) if bonus_scores else 0,
            'bonus_avg': round(bonus_total / len(bonus_scores), 1) if bonus_scores else 0,
            'needs_recalculation': normal_total != 100 and normal_questions.count() > 0
        }

    def validate_scoring(self):
        """Validate the current scoring setup and return issues"""
        issues = []
        distribution = self.get_score_distribution()

        if distribution['normal_count'] == 0:
            issues.append("No normal questions found. Add at least one normal question.")
        elif not distribution['is_balanced']:
            issues.append(f"Normal questions total {distribution['normal_total']} points instead of 100. Recalculation needed.")

        if distribution['normal_count'] > 0:
            # Check for zero-point questions
            zero_point_questions = self.levels.filter(score=0)
            if zero_point_questions.exists():
                issues.append(f"{zero_point_questions.count()} questions have 0 points.")

        return issues

    def auto_fix_scoring(self):
        """Automatically fix common scoring issues"""
        issues = self.validate_scoring()

        if any("Recalculation needed" in issue for issue in issues):
            self.calculate_automatic_scores()
            return True

        return False

    class Meta:
        ordering = ['name']


class ExamInstance(models.Model):
    """
    Represents a specific exam session/instance.
    Multiple instances can run simultaneously with different versions.
    """
    name = models.CharField(max_length=100, unique=True)  # e.g., "Class A Morning", "Class B Afternoon"
    description = models.TextField(blank=True)
    version = models.ForeignKey(TestVersion, on_delete=models.CASCADE, related_name='instances')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_instances')

    # Registration control
    registration_open = models.BooleanField(default=True)
    registration_closes_at = models.DateTimeField()  # Auto-set to 1 hour after creation

    # Instance control
    is_active = models.BooleanField(default=True)
    exam_paused = models.BooleanField(default=False)

    # Export tracking
    exported_at = models.DateTimeField(null=True, blank=True)
    exported_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='exported_instances')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Auto-set registration close time to 1 hour after creation
        if not self.pk and not self.registration_closes_at:
            self.registration_closes_at = timezone.now() + timedelta(hours=1)
        super().save(*args, **kwargs)

    @property
    def registration_auto_closed(self):
        """Check if registration should be automatically closed"""
        return timezone.now() > self.registration_closes_at

    @property
    def can_register(self):
        """Check if students can register for this instance"""
        return self.registration_open and not self.registration_auto_closed and self.is_active

    @property
    def student_count(self):
        """Get number of students registered for this instance"""
        return self.players.count()

    @property
    def active_student_count(self):
        """Get number of students currently taking the exam"""
        return self.players.filter(start_time__isnull=False, end_time__isnull=True).count()

    @property
    def can_be_deleted(self):
        """Check if instance can be deleted (must be exported first if has students)"""
        if self.student_count == 0:
            return True
        return self.exported_at is not None

    @property
    def is_exported(self):
        """Check if instance has been exported"""
        return self.exported_at is not None

    def __str__(self):
        return f"{self.name} ({self.version.name})"

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Exam Instance"
        verbose_name_plural = "Exam Instances"


class Level(models.Model):
    """
    Enhanced question model supporting multiple question types.
    Maintains backward compatibility with existing text-based questions.
    """

    # Question type choices
    QUESTION_TYPES = [
        ('text', 'Text Answer'),
        ('multiple_choice', 'Multiple Choice'),
        ('true_false', 'True/False'),
        ('code_execution', 'Code Execution'),
        ('drag_drop', 'Drag and Drop'),
    ]

    # Basic question information
    name = models.CharField(max_length=200, help_text="Question title/identifier")
    description = models.TextField(help_text="The actual question text")
    question_type = models.CharField(
        max_length=20,
        choices=QUESTION_TYPES,
        default='text',
        help_text="Type of question"
    )

    # Question ordering and scoring
    order = models.IntegerField(help_text="Order in the version")
    score = models.IntegerField(default=5, help_text="Points for this question")
    version = models.ForeignKey(TestVersion, on_delete=models.CASCADE, related_name='levels')
    is_bonus = models.BooleanField(default=False, help_text="Bonus questions don't count toward base 100 points")

    # Answer fields
    correct_answer = models.CharField(
        max_length=500,
        help_text="Correct answer for text questions or correct choice letter for multiple choice"
    )

    # Multiple choice options (JSON field for flexibility)
    choices = models.JSONField(
        null=True,
        blank=True,
        help_text="JSON object with choices for multiple choice questions. Format: {'A': 'Option 1', 'B': 'Option 2', ...}"
    )

    # Additional fields
    explanation = models.TextField(
        blank=True,
        help_text="Optional explanation shown after answering (for learning purposes)"
    )

    # Code execution fields
    programming_language = models.CharField(
        max_length=50,
        blank=True,
        default='python',
        help_text="Programming language for code execution questions"
    )
    starter_code = models.TextField(
        blank=True,
        help_text="Initial code template for students"
    )
    test_cases = models.JSONField(
        null=True,
        blank=True,
        help_text="Test cases for auto-grading code execution questions"
    )

    # Drag and drop fields
    drag_items = models.JSONField(
        null=True,
        blank=True,
        help_text="Items to drag in drag-and-drop questions"
    )
    drop_zones = models.JSONField(
        null=True,
        blank=True,
        help_text="Drop zones/targets for drag-and-drop questions"
    )
    correct_matches = models.JSONField(
        null=True,
        blank=True,
        help_text="Correct item-zone pairs for drag-and-drop questions"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.version.name} - {self.order}: {self.name}"

    def get_question_type_display_icon(self):
        """Return an icon for the question type"""
        icons = {
            'text': '📝',
            'multiple_choice': '🔘',
            'true_false': '✅',
            'code_execution': '💻',
            'drag_drop': '🎯',
        }
        return icons.get(self.question_type, '❓')

    def get_formatted_choices(self):
        """Return formatted choices for multiple choice questions"""
        if self.question_type == 'multiple_choice' and self.choices:
            return self.choices
        elif self.question_type == 'true_false':
            return {'A': 'True', 'B': 'False'}
        return None

    def validate_answer(self, user_answer):
        """Validate user answer based on question type"""
        if not user_answer:
            return False

        user_answer = user_answer.strip()
        correct_answer = self.correct_answer.strip()

        if self.question_type == 'text':
            # Case-insensitive comparison for text answers
            return user_answer.lower() == correct_answer.lower()
        elif self.question_type in ['multiple_choice', 'true_false']:
            # Exact match for choice-based questions (A, B, C, D)
            return user_answer.upper() == correct_answer.upper()

        return False

    def get_choice_text(self, choice_letter):
        """Get the text for a specific choice letter"""
        choices = self.get_formatted_choices()
        if choices and choice_letter.upper() in choices:
            return choices[choice_letter.upper()]
        return None

    class Meta:
        ordering = ['version', 'order']
        unique_together = ['version', 'order']


class Player(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    instance = models.ForeignKey(ExamInstance, on_delete=models.CASCADE, related_name='players')
    current_level = models.ForeignKey(Level, on_delete=models.SET_NULL, null=True, blank=True)
    score = models.FloatField(default=0.0)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    is_exam_paused = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    # Data protection fields
    is_anonymized = models.BooleanField(default=False)
    anonymized_at = models.DateTimeField(null=True, blank=True)
    data_retention_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.instance.name}"

    @property
    def version(self):
        """Backward compatibility - get version through instance"""
        return self.instance.version

    @property
    def total_time_seconds(self):
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0

    class Meta:
        unique_together = ['user', 'instance']


class PlayerOnLevel(models.Model):
    player = models.ForeignKey(Player, on_delete=models.CASCADE, related_name='level_attempts')
    level = models.ForeignKey(Level, on_delete=models.CASCADE)
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    attempts = models.IntegerField(default=0)
    correctly_answered = models.BooleanField(default=False)
    time_spent_seconds = models.IntegerField(default=0)

    def __str__(self):
        return f"{self.player.user.username} - {self.level.name}"

    @property
    def duration_seconds(self):
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0

    class Meta:
        unique_together = ['player', 'level']


class ScoringConfig(models.Model):
    # General Test Settings
    num_standard_questions = models.IntegerField(default=18)
    points_per_standard_question = models.FloatField(default=5.0)
    num_bonus_questions = models.IntegerField(default=2)
    points_per_bonus_question = models.FloatField(default=5.0)

    # Incorrect Attempt Penalties
    incorrect_attempts_penalized_from = models.IntegerField(default=2)
    attempt_penalty_per_mistake = models.FloatField(default=0.5)
    max_attempt_penalty_per_question = models.FloatField(default=1.5)

    # Time Penalties
    time_penalty_threshold_minutes = models.IntegerField(default=5)
    time_penalty_per_minute = models.FloatField(default=0.25)
    max_time_penalty_per_question = models.FloatField(default=1.5)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Scoring Config - {self.created_at.strftime('%Y-%m-%d')}"

    class Meta:
        verbose_name = "Scoring Configuration"
        verbose_name_plural = "Scoring Configurations"


class AppConfig(models.Model):
    # Server Settings
    host = models.CharField(max_length=50, default='0.0.0.0')
    port = models.IntegerField(default=5000)
    debug = models.BooleanField(default=True)

    # Email Settings
    email_backend = models.CharField(max_length=200, default='django.core.mail.backends.smtp.EmailBackend')
    email_host = models.CharField(max_length=100, blank=True)
    email_port = models.IntegerField(default=587)
    email_use_tls = models.BooleanField(default=True)
    email_host_user = models.CharField(max_length=100, blank=True)
    email_host_password = models.CharField(max_length=500, blank=True)  # Increased for encrypted data
    require_email_verification = models.BooleanField(default=True)

    # Export Settings
    last_export_time = models.DateTimeField(null=True, blank=True)
    export_completed = models.BooleanField(default=False)

    # Exam Control
    exam_paused = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def get_config(cls):
        """Get or create the application configuration"""
        config, created = cls.objects.get_or_create(pk=1)
        return config

    def __str__(self):
        return f"App Config - {self.host}:{self.port}"

    class Meta:
        verbose_name = "Application Configuration"
        verbose_name_plural = "Application Configurations"


# Signal handlers for automatic score recalculation
@receiver(post_delete, sender='exam.Level')
def recalculate_scores_on_question_delete(sender, instance, **kwargs):
    """Automatically recalculate scores when a question is deleted"""
    if instance.version:
        try:
            instance.version.calculate_automatic_scores()
        except Exception:
            # Silently fail to avoid breaking the deletion process
            pass


class Announcement(models.Model):
    """System announcements for students and teachers"""

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    AUDIENCE_CHOICES = [
        ('all', 'All Users'),
        ('students', 'Students Only'),
        ('teachers', 'Teachers Only'),
        ('instance', 'Specific Instance'),
    ]

    title = models.CharField(max_length=200)
    message = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='normal')
    audience = models.CharField(max_length=20, choices=AUDIENCE_CHOICES, default='all')
    target_instance = models.ForeignKey(
        'ExamInstance',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Required if audience is 'instance'"
    )

    is_active = models.BooleanField(default=True)
    show_popup = models.BooleanField(default=False, help_text="Show as popup notification")
    auto_dismiss = models.BooleanField(default=False, help_text="Auto-dismiss after reading")
    dismiss_after_seconds = models.IntegerField(default=10, help_text="Auto-dismiss time in seconds")

    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.title} ({self.get_priority_display()})"

    @property
    def is_expired(self):
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    @property
    def priority_icon(self):
        icons = {
            'low': '💬',
            'normal': '📢',
            'high': '⚠️',
            'urgent': '🚨'
        }
        return icons.get(self.priority, '📢')

    class Meta:
        ordering = ['-created_at']


class AnnouncementRead(models.Model):
    """Track which users have read announcements"""

    announcement = models.ForeignKey(Announcement, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    read_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['announcement', 'user']


class EmailTemplate(models.Model):
    """Email templates for automated communications"""

    TEMPLATE_TYPES = [
        ('welcome', 'Welcome Email'),
        ('exam_start', 'Exam Started'),
        ('exam_complete', 'Exam Completed'),
        ('results', 'Results Available'),
        ('reminder', 'Exam Reminder'),
        ('password_reset', 'Password Reset'),
        ('instance_created', 'Instance Created'),
        ('registration_open', 'Registration Open'),
        ('custom', 'Custom Template'),
    ]

    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    subject = models.CharField(max_length=200)
    html_content = models.TextField(help_text="HTML email content with template variables")
    text_content = models.TextField(blank=True, help_text="Plain text fallback")

    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    class Meta:
        ordering = ['template_type', 'name']


class ChatRoom(models.Model):
    """Chat rooms for real-time communication"""

    ROOM_TYPES = [
        ('global', 'Global Chat'),
        ('instance', 'Instance Chat'),
        ('support', 'Support Chat'),
        ('private', 'Private Chat'),
    ]

    name = models.CharField(max_length=100)
    room_type = models.CharField(max_length=20, choices=ROOM_TYPES, default='instance')
    instance = models.ForeignKey(
        'ExamInstance',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Required for instance chat rooms"
    )

    is_active = models.BooleanField(default=True)
    allow_students = models.BooleanField(default=True)
    allow_teachers = models.BooleanField(default=True)

    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.get_room_type_display()})"


class ChatMessage(models.Model):
    """Individual chat messages"""

    MESSAGE_TYPES = [
        ('text', 'Text Message'),
        ('system', 'System Message'),
        ('file', 'File Attachment'),
        ('image', 'Image'),
    ]

    room = models.ForeignKey(ChatRoom, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE)
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='text')
    content = models.TextField()

    # File attachments
    file_attachment = models.FileField(upload_to='chat_files/', null=True, blank=True)
    file_name = models.CharField(max_length=255, blank=True)
    file_size = models.IntegerField(null=True, blank=True)

    is_edited = models.BooleanField(default=False)
    edited_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.sender.username}: {self.content[:50]}..."

    class Meta:
        ordering = ['created_at']


class NotificationPreference(models.Model):
    """User notification preferences"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_preferences')

    # Email notifications
    email_announcements = models.BooleanField(default=True)
    email_exam_updates = models.BooleanField(default=True)
    email_results = models.BooleanField(default=True)
    email_chat_mentions = models.BooleanField(default=True)

    # In-app notifications
    show_popup_announcements = models.BooleanField(default=True)
    show_chat_notifications = models.BooleanField(default=True)
    show_exam_notifications = models.BooleanField(default=True)

    # SMS notifications (if implemented)
    sms_urgent_only = models.BooleanField(default=True)
    phone_number = models.CharField(max_length=20, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Notification preferences for {self.user.username}"
