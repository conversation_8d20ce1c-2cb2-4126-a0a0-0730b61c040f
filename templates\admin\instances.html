{% extends 'admin/admin_base.html' %}

{% block title %}Exam Instances Management{% endblock %}

{% block content %}
{% csrf_token %}

<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">instance_manager.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    🎯 Exam Instances Management
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > Monitor and manage all exam instances across all teachers
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="admin-grid admin-grid-4">
    <div class="admin-card">
        <h3>📊 Total Instances</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ stats.total_instances }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            All exam instances
        </p>
    </div>
    
    <div class="admin-card">
        <h3>✅ Active Instances</h3>
        <div style="font-size: 2rem; color: var(--success); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ stats.active_instances }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Currently running
        </p>
    </div>
    
    <div class="admin-card">
        <h3>🔓 Open Registration</h3>
        <div style="font-size: 2rem; color: var(--warning); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ stats.open_registration }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Accepting students
        </p>
    </div>
    
    <div class="admin-card">
        <h3>👥 Total Students</h3>
        <div style="font-size: 2rem; color: var(--accent); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ stats.total_students }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Across all instances
        </p>
    </div>
</div>

<!-- Instances List -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">instances_list.json</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <h3 style="color: var(--primary);">🎯 All Exam Instances ({{ instances.count }})</h3>
        </div>
        
        {% if instances %}
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; font-family: 'JetBrains Mono', monospace;">
                    <thead>
                        <tr style="border-bottom: 2px solid var(--gray-750);">
                            <th style="padding: 1rem; text-align: left; color: var(--primary);">Instance</th>
                            <th style="padding: 1rem; text-align: left; color: var(--primary);">Version</th>
                            <th style="padding: 1rem; text-align: left; color: var(--primary);">Teacher</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Students</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Status</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Registration</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Created</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for instance in instances %}
                            <tr style="border-bottom: 1px solid var(--gray-850);">
                                <td style="padding: 1rem;">
                                    <div>
                                        <strong style="color: var(--text-primary);">{{ instance.name }}</strong>
                                        {% if instance.description %}
                                            <div style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.25rem;">
                                                {{ instance.description|truncatechars:50 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </td>
                                <td style="padding: 1rem;">
                                    <span style="color: var(--primary);">{{ instance.version.name }}</span>
                                </td>
                                <td style="padding: 1rem;">
                                    <span style="color: var(--text-secondary);">{{ instance.created_by.username }}</span>
                                </td>
                                <td style="padding: 1rem; text-align: center;">
                                    <div style="color: var(--text-primary);">
                                        <strong>{{ instance.total_students }}</strong> total
                                    </div>
                                    <div style="font-size: 0.8rem; color: var(--text-muted);">
                                        {{ instance.active_students }} active, {{ instance.completed_students }} done
                                    </div>
                                </td>
                                <td style="padding: 1rem; text-align: center;">
                                    {% if instance.is_active %}
                                        {% if instance.exam_paused %}
                                            <span style="color: var(--warning);">⏸️ Paused</span>
                                        {% else %}
                                            <span style="color: var(--success);">✅ Active</span>
                                        {% endif %}
                                    {% else %}
                                        <span style="color: var(--error);">❌ Inactive</span>
                                    {% endif %}
                                </td>
                                <td style="padding: 1rem; text-align: center;">
                                    {% if instance.registration_open %}
                                        <span style="color: var(--success);">🔓 Open</span>
                                    {% else %}
                                        <span style="color: var(--error);">🔒 Closed</span>
                                    {% endif %}
                                    <div style="font-size: 0.8rem; color: var(--text-muted); margin-top: 0.25rem;">
                                        Closes: {{ instance.registration_closes_at|date:"M d, H:i" }}
                                    </div>
                                </td>
                                <td style="padding: 1rem; text-align: center;">
                                    <div style="color: var(--text-secondary);">{{ instance.created_at|date:"M d, Y" }}</div>
                                    <div style="font-size: 0.8rem; color: var(--text-muted);">{{ instance.created_at|date:"H:i" }}</div>
                                </td>
                                <td style="padding: 1rem; text-align: center;">
                                    <div style="display: flex; gap: 0.5rem; justify-content: center; flex-wrap: wrap;">
                                        <a href="{% url 'instance_questions' instance.id %}" class="btn btn-secondary" style="font-size: 0.8rem; padding: 0.5rem;">
                                            📝 View
                                        </a>
                                        <a href="{% url 'export_instance_csv' instance.id %}" class="btn btn-terminal" style="font-size: 0.8rem; padding: 0.5rem;">
                                            📊 Export
                                        </a>
                                        {% if instance.can_be_deleted %}
                                            <button onclick="deleteInstance({{ instance.id }}, '{{ instance.name }}')" class="btn btn-danger" style="font-size: 0.8rem; padding: 0.5rem;">
                                                🗑️ Delete
                                            </button>
                                        {% else %}
                                            <button class="btn btn-disabled" style="font-size: 0.8rem; padding: 0.5rem;" disabled title="Export data first">
                                                🗑️ Delete
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <!-- Empty State -->
            <div style="text-align: center; padding: 4rem 2rem; color: var(--text-muted);">
                <div style="font-size: 4rem; margin-bottom: 1rem;">🎯</div>
                <h3 style="color: var(--text-secondary); margin-bottom: 1rem;">No Exam Instances Found</h3>
                <p style="margin-bottom: 2rem;">Teachers haven't created any exam instances yet.</p>
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function deleteInstance(instanceId, instanceName) {
    if (confirm(`Are you sure you want to delete instance "${instanceName}"?\n\nThis action cannot be undone.`)) {
        fetch(`/teacher/delete-instance/${instanceId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Failed to delete instance', 'error');
        });
    }
}

function showMessage(message, type) {
    // Simple message display - you can enhance this
    alert(message);
}
</script>
{% endblock %}
