{% extends 'exam/base.html' %}

{% block title %}KERNELiOS - Secure Exam System{% endblock %}

{% block content %}
<!-- Main Content Section -->
<div class="main-section">
    <!-- Hero Content (Centered) -->
    <div class="hero-content">
        <div class="hero-logo">
            <img src="/static/assets/logo.png" alt="KERNELiOS" style="width: 120px; height: 120px; filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));">
        </div>

        <h1 class="hero-title">
            KERNELiOS Exam System
        </h1>

        <p class="hero-subtitle">
            Advanced cybersecurity examination platform with real-time monitoring and secure authentication.
        </p>

        <div class="hero-actions">
            <a href="{% url 'login' %}" class="btn btn-primary btn-hero">
                🔐 Login to Exam
            </a>
            <a href="{% url 'register' %}" class="btn btn-secondary btn-hero">
                📝 Register for Exam
            </a>
        </div>
    </div>

    <!-- System Status Terminal (Top Right) -->
    <div class="status-terminal">
        <div class="terminal-window terminal-md">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">exam_system.sh</div>
            </div>
            <div class="terminal-content" id="systemTerminal">
                <div><span class="terminal-prompt">kernelios@system:~$</span> <span class="terminal-text">./exam_system.sh --status</span></div>
                <div class="terminal-line">Initializing KERNELiOS...</div>
                <div class="terminal-line">🟢 Platform: <span style="color: var(--success);">ONLINE</span></div>
                <div class="terminal-line">🟢 Security: <span style="color: var(--success);">ACTIVE</span></div>
                <div class="terminal-line">🟢 Auth: <span style="color: var(--success);">SECURE</span></div>
                <div class="terminal-line">🔵 Sessions: <span style="color: var(--primary);">127</span></div>
                <div class="terminal-line">📊 Load: <span style="color: var(--success);">Normal</span></div>
                <div class="terminal-line">🛡️ Firewall: <span style="color: var(--success);">Protected</span></div>
                <div class="terminal-line">⚡ Ready for exams...</div>
                <div style="margin-top: 0.5rem;"><span class="terminal-cursor"></span></div>
            </div>
        </div>
    </div>

    <!-- Instructions Section (Bottom - 2 Columns) -->
    <div class="instructions-section">
        <div class="card card-terminal">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">exam_instructions.txt</div>
            </div>
            <div class="terminal-content">
                <h2 class="instructions-title">📋 Exam Protocol</h2>
                <div class="instructions-grid">
                    <div class="instructions-column">
                        <div class="instruction-item">
                            <span class="instruction-bullet">•</span>
                            <span>You must answer questions in sequential order</span>
                        </div>
                        <div class="instruction-item">
                            <span class="instruction-bullet">•</span>
                            <span>All answers are masked for security purposes</span>
                        </div>
                        <div class="instruction-item">
                            <span class="instruction-bullet">•</span>
                            <span>Time is tracked for each question</span>
                        </div>
                    </div>
                    <div class="instructions-column">
                        <div class="instruction-item">
                            <span class="instruction-bullet">•</span>
                            <span>Scores are calculated automatically</span>
                        </div>
                        <div class="instruction-item">
                            <span class="instruction-bullet">•</span>
                            <span>Progress is monitored in real-time</span>
                        </div>
                        <div class="instruction-item">
                            <span class="instruction-bullet">•</span>
                            <span>Secure environment ensures exam integrity</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<style>
/* Main Section Styles */
.main-section {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: calc(100vh - 200px);
    display: grid;
    grid-template-areas:
        "hero hero hero"
        "instructions instructions instructions";
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto 1fr;
    gap: 2rem;
    align-items: start;
}

.hero-content {
    grid-area: hero;
    text-align: center;
    padding-top: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 10;
}

.hero-logo {
    margin-bottom: 1.5rem;
}

.hero-title {
    font-size: 3rem;
    font-family: 'Orbitron', sans-serif;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-hero {
    font-size: 1rem;
    padding: 0.875rem 1.75rem;
    min-width: 180px;
}

.status-terminal {
    position: fixed;
    top: 15%;
    left: 2%;
    max-width: 280px;
    z-index: 5;
    opacity: 0.9;
}

.instructions-section {
    grid-area: instructions;
    margin-top: 2rem;
    width: 100%;
}

/* Instructions Section */
.instructions-section {
    padding: 0;
}

.instructions-title {
    color: var(--primary);
    font-family: 'Orbitron', sans-serif;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.instructions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.instructions-column {
    font-family: 'Fira Code', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
}

.instruction-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.25rem 0;
}

.instruction-bullet {
    color: var(--primary);
    font-weight: bold;
    margin-top: 0.1rem;
}

.instruction-item span:last-child {
    color: var(--text-primary);
    flex: 1;
}

.terminal-line {
    margin: 0.3rem 0;
    color: var(--text-terminal);
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .status-terminal {
        max-width: 250px;
        left: 1%;
    }
}

@media (max-width: 1024px) {
    .main-section {
        grid-template-areas:
            "hero"
            "instructions";
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 2rem 1.5rem;
    }

    .status-terminal {
        display: none;
    }

    .instructions-grid {
        gap: 1.5rem;
    }

    .hero-title {
        font-size: 2.75rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.25rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn-hero {
        min-width: 250px;
    }

    .main-section {
        padding: 1.5rem 1rem;
        gap: 1.5rem;
    }

    .instructions-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .instructions-title {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .hero-subtitle {
        font-size: 0.95rem;
    }

    .btn-hero {
        min-width: 100%;
        font-size: 0.9rem;
        padding: 0.75rem 1.5rem;
    }

    .main-section {
        padding: 1rem 0.5rem;
    }

    .hero-content {
        padding-top: 0.5rem;
    }

    .instructions-section {
        margin-top: 1rem;
    }

    .instruction-item {
        font-size: 0.8rem;
    }
}

@media (max-width: 320px) {
    .hero-title {
        font-size: 1.5rem;
    }

    .hero-subtitle {
        font-size: 0.85rem;
    }

    .btn-hero {
        font-size: 0.85rem;
        padding: 0.65rem 1.25rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate system terminal content
    setTimeout(() => {
        const lines = document.querySelectorAll('#systemTerminal .terminal-line');
        lines.forEach((line, index) => {
            setTimeout(() => {
                line.style.opacity = '0';
                line.style.transform = 'translateX(-10px)';
                line.style.transition = 'all 0.5s ease';
                setTimeout(() => {
                    line.style.opacity = '1';
                    line.style.transform = 'translateX(0)';
                }, 50);
            }, index * 300);
        });
    }, 1500);

    // Animate hero content
    setTimeout(() => {
        const heroElements = document.querySelectorAll('.hero-content > *');
        heroElements.forEach((element, index) => {
            setTimeout(() => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                element.style.transition = 'all 0.6s ease';
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 50);
            }, index * 200);
        });
    }, 500);
});
</script>
{% endblock %}
