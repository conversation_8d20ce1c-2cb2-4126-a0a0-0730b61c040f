{% extends 'admin/base_admin.html' %}

{% block title %}Maintenance Tools - KERNELiOS Admin{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="admin-header-content">
        <h1 style="color: var(--primary); margin: 0; display: flex; align-items: center; gap: 1rem;">
            <span style="font-size: 2rem;">🔧</span>
            Maintenance & Monitoring Tools
        </h1>
        <p style="color: var(--text-muted); margin: 0.5rem 0 0 0;">
            System maintenance, health monitoring, and automated testing
        </p>
    </div>
    
    <div class="admin-header-actions">
        {% if maintenance_mode %}
            <form method="post" style="display: inline;">
                {% csrf_token %}
                <button type="submit" name="action" value="disable_maintenance" class="btn btn-warning">
                    🔓 Disable Maintenance Mode
                </button>
            </form>
        {% else %}
            <button onclick="enableMaintenanceMode()" class="btn btn-secondary">
                🔒 Enable Maintenance Mode
            </button>
        {% endif %}
    </div>
</div>

<div class="admin-content">
    <!-- Maintenance Mode Status -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h3 style="color: var(--text-primary); margin: 0;">🚧 Maintenance Mode</h3>
        </div>
        
        <div class="maintenance-status {{ maintenance_mode|yesno:'enabled,disabled' }}">
            <div class="status-icon">
                {% if maintenance_mode %}🔒{% else %}🔓{% endif %}
            </div>
            <div class="status-details">
                <div class="status-title">
                    Maintenance Mode: {{ maintenance_mode|yesno:'ENABLED,DISABLED' }}
                </div>
                {% if maintenance_mode %}
                    <div class="status-message">{{ maintenance_message }}</div>
                {% else %}
                    <div class="status-message">System is available to users</div>
                {% endif %}
            </div>
        </div>
        
        {% if not maintenance_mode %}
            <div class="maintenance-controls">
                <form method="post" id="maintenanceForm">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="enable_maintenance">
                    
                    <div class="form-group">
                        <label for="message" class="form-label">Maintenance Message</label>
                        <textarea id="message" name="message" class="form-textarea" rows="3"
                                  placeholder="System is under maintenance. Please try again later.">System is under maintenance. Please try again later.</textarea>
                    </div>
                </form>
            </div>
        {% endif %}
    </div>

    <!-- System Status Overview -->
    <div class="admin-grid admin-grid-3">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">💓 System Health</h3>
            </div>
            
            <div class="health-metrics">
                <div class="health-item">
                    <span class="health-label">Database:</span>
                    <span class="health-value {{ system_status.database.status }}">
                        {{ system_status.database.status|title }}
                    </span>
                </div>
                
                <div class="health-item">
                    <span class="health-label">Cache:</span>
                    <span class="health-value {{ system_status.cache.status }}">
                        {{ system_status.cache.status|title }}
                    </span>
                </div>
                
                <div class="health-item">
                    <span class="health-label">Disk Space:</span>
                    <span class="health-value {{ system_status.disk_space.status }}">
                        {{ system_status.disk_space.usage_percent|floatformat:1 }}%
                    </span>
                </div>
                
                <div class="health-item">
                    <span class="health-label">Memory:</span>
                    <span class="health-value {{ system_status.memory_usage.status }}">
                        {{ system_status.memory_usage.used_percent|floatformat:1 }}%
                    </span>
                </div>
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📊 Activity</h3>
            </div>
            
            <div class="activity-metrics">
                <div class="activity-item">
                    <div class="activity-value">{{ system_status.active_sessions.active_exams }}</div>
                    <div class="activity-label">Active Exams</div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-value">{{ system_status.active_sessions.recent_logins }}</div>
                    <div class="activity-label">Recent Logins</div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-value">{{ system_status.uptime.uptime_human }}</div>
                    <div class="activity-label">Uptime</div>
                </div>
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">🔧 Quick Actions</h3>
            </div>
            
            <div class="quick-actions">
                <form method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" name="action" value="run_health_tests" class="btn btn-secondary btn-sm">
                        🧪 Run Health Tests
                    </button>
                </form>
                
                <button onclick="openMonitoring()" class="btn btn-secondary btn-sm">
                    📊 System Monitor
                </button>
                
                <button onclick="openBackups()" class="btn btn-secondary btn-sm">
                    💾 Backups
                </button>
                
                <button onclick="openSecurity()" class="btn btn-secondary btn-sm">
                    🔒 Security Audit
                </button>
            </div>
        </div>
    </div>

    <!-- Version Information -->
    <div class="admin-grid admin-grid-2">
        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">📋 Version Information</h3>
            </div>
            
            <div class="version-info">
                <div class="version-item">
                    <span class="version-label">Current Version:</span>
                    <span class="version-value">{{ current_version }}</span>
                </div>
                
                {% if git_info.available %}
                    <div class="version-item">
                        <span class="version-label">Git Branch:</span>
                        <span class="version-value">{{ git_info.branch }}</span>
                    </div>
                    
                    <div class="version-item">
                        <span class="version-label">Commit Hash:</span>
                        <span class="version-value">{{ git_info.commit_hash }}</span>
                    </div>
                    
                    <div class="version-item">
                        <span class="version-label">Last Commit:</span>
                        <span class="version-value">{{ git_info.commit_message|truncatechars:50 }}</span>
                    </div>
                {% endif %}
            </div>
            
            <div class="version-actions">
                <form method="post" class="version-form">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="set_version">
                    
                    <div class="form-group">
                        <label for="version" class="form-label">Set Version</label>
                        <div class="input-group">
                            <input type="text" id="version" name="version" class="form-input" 
                                   placeholder="e.g., 2.1.0" value="{{ current_version }}">
                            <button type="submit" class="btn btn-secondary btn-sm">Update</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="admin-card">
            <div class="admin-card-header">
                <h3 style="color: var(--text-primary); margin: 0;">🧪 Health Test Results</h3>
            </div>
            
            {% if test_results %}
                <div class="test-results">
                    <div class="test-summary">
                        <div class="test-stat passed">
                            <div class="stat-value">{{ test_results.passed }}</div>
                            <div class="stat-label">Passed</div>
                        </div>
                        
                        <div class="test-stat failed">
                            <div class="stat-value">{{ test_results.failed }}</div>
                            <div class="stat-label">Failed</div>
                        </div>
                        
                        <div class="test-stat total">
                            <div class="stat-value">{{ test_results.total_tests }}</div>
                            <div class="stat-label">Total</div>
                        </div>
                    </div>
                    
                    <div class="test-details">
                        {% for test_name, test_data in test_results.tests.items %}
                            <div class="test-item {{ test_data.status }}">
                                <div class="test-name">{{ test_name|title }}</div>
                                <div class="test-status">{{ test_data.status|title }}</div>
                                <div class="test-time">{{ test_data.execution_time_ms|floatformat:1 }}ms</div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-text">No test results available</div>
                    <div class="empty-subtext">Run health tests to see system status</div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Maintenance Mode Modal -->
<div id="maintenanceModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Enable Maintenance Mode</h3>
            <button type="button" class="modal-close" onclick="closeMaintenanceModal()">×</button>
        </div>
        
        <div class="modal-body">
            <p>This will prevent users from accessing the system. Are you sure you want to enable maintenance mode?</p>
            
            <div class="form-group">
                <label for="modalMessage" class="form-label">Maintenance Message</label>
                <textarea id="modalMessage" class="form-textarea" rows="3"
                          placeholder="System is under maintenance. Please try again later.">System is under maintenance. Please try again later.</textarea>
            </div>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeMaintenanceModal()">Cancel</button>
            <button type="button" class="btn btn-warning" onclick="confirmMaintenanceMode()">Enable Maintenance Mode</button>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.maintenance-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 2px solid;
    margin-bottom: 1rem;
}

.maintenance-status.enabled {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
}

.maintenance-status.disabled {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
}

.status-icon {
    font-size: 2rem;
}

.status-details {
    flex: 1;
}

.status-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.status-message {
    color: var(--text-muted);
}

.maintenance-controls {
    margin-top: 1rem;
}

.health-metrics,
.activity-metrics {
    display: grid;
    gap: 1rem;
}

.health-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.health-label {
    color: var(--text-muted);
}

.health-value {
    font-weight: 600;
}

.health-value.healthy { color: #22c55e; }
.health-value.warning { color: #fbbf24; }
.health-value.error { color: #ef4444; }

.activity-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.activity-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.activity-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.quick-actions {
    display: grid;
    gap: 0.5rem;
}

.version-info {
    display: grid;
    gap: 1rem;
    margin-bottom: 1rem;
}

.version-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.version-label {
    color: var(--text-muted);
}

.version-value {
    color: var(--text-primary);
    font-weight: 600;
}

.input-group {
    display: flex;
    gap: 0.5rem;
}

.input-group .form-input {
    flex: 1;
}

.test-results {
    display: grid;
    gap: 1rem;
}

.test-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.test-stat {
    text-align: center;
    padding: 1rem;
    border-radius: 0.5rem;
}

.test-stat.passed {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.test-stat.failed {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.test-stat.total {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.test-details {
    display: grid;
    gap: 0.5rem;
}

.test-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border-left: 4px solid;
}

.test-item.passed {
    background: rgba(34, 197, 94, 0.05);
    border-left-color: #22c55e;
}

.test-item.failed {
    background: rgba(239, 68, 68, 0.05);
    border-left-color: #ef4444;
}

.test-item.error {
    background: rgba(249, 115, 22, 0.05);
    border-left-color: #f97316;
}

.test-name {
    font-weight: 500;
    color: var(--text-primary);
}

.test-status {
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 600;
}

.test-time {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
}

.empty-text {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.empty-subtext {
    font-size: 0.9rem;
    font-style: italic;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--dark-card);
    border-radius: 0.75rem;
    border: 2px solid var(--primary);
    padding: 0;
    max-width: 500px;
    width: 90vw;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function enableMaintenanceMode() {
    document.getElementById('maintenanceModal').style.display = 'flex';
}

function closeMaintenanceModal() {
    document.getElementById('maintenanceModal').style.display = 'none';
}

function confirmMaintenanceMode() {
    const message = document.getElementById('modalMessage').value;
    
    // Update the form and submit
    document.getElementById('message').value = message;
    document.getElementById('maintenanceForm').submit();
}

function openMonitoring() {
    window.open('{% url "admin_panel:admin_monitoring_dashboard" %}', '_blank');
}

function openBackups() {
    window.open('{% url "admin_panel:admin_backup_management" %}', '_blank');
}

function openSecurity() {
    window.open('{% url "admin_panel:admin_security_audit" %}', '_blank');
}

// Close modal when clicking outside
document.getElementById('maintenanceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeMaintenanceModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeMaintenanceModal();
    }
});
</script>
{% endblock %}
